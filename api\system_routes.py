"""
PTT 自動登入系統 - 系統相關 API 路由
"""

from flask import request, jsonify
from . import api_bp
from config import Config
from logger import ptt_logger
from crawler import PTTCrawler
import json

@api_bp.route('/system/status', methods=['GET'])
def get_system_status():
    """取得系統狀態
    
    Returns:
        JSON: 系統狀態資訊
    """
    try:
        ptt_logger.info("API 請求: 取得系統狀態")
        
        # 取得配置摘要
        config_summary = Config.get_config_summary()
        
        # 檢查配置錯誤
        ptt_config_errors = Config.validate_config()
        cosmos_config_errors = Config.validate_cosmos_config()
        
        # 檢查圖形資料庫連接
        from graph_query import GraphQuery
        from graph_writer import GraphWriter
        
        query_engine = GraphQuery()
        writer = GraphWriter()
        
        status = {
            'system': {
                'name': 'PTT 自動登入系統',
                'version': '1.0.0',
                'timestamp': ptt_logger.get_timestamp()
            },
            'configuration': {
                'ptt_configured': len(ptt_config_errors) == 0,
                'cosmos_configured': len(cosmos_config_errors) == 0,
                'config_summary': config_summary,
                'errors': ptt_config_errors + cosmos_config_errors
            },
            'services': {
                'graph_query': {
                    'available': query_engine.is_connected,
                    'status': '已連接' if query_engine.is_connected else '未連接'
                },
                'graph_writer': {
                    'available': writer.is_connected,
                    'status': '已連接' if writer.is_connected else '未連接'
                }
            }
        }
        
        return jsonify({
            'success': True,
            'data': status
        })
        
    except Exception as e:
        ptt_logger.error(f"取得系統狀態 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/system/recrawl-post', methods=['POST'])
def recrawl_post():
    """重新爬取特定文章的推文

    Request Body:
        {
            "post_id": "stock.1eLC2coG",
            "board": "stock"
        }

    Returns:
        JSON: 重新爬取結果
    """
    try:
        ptt_logger.info("API 請求: 重新爬取文章推文")

        data = request.get_json() or {}
        post_id = data.get('post_id', '').strip()
        board = data.get('board', '').strip()

        if not post_id or not board:
            return jsonify({
                'success': False,
                'error': '請提供文章 ID 和看板名稱'
            }), 400

        # 檢查 PTT 配置
        ptt_config_errors = Config.validate_config()
        if ptt_config_errors:
            return jsonify({
                'success': False,
                'error': 'PTT 配置不完整',
                'details': ptt_config_errors
            }), 400

        # 執行重新爬取
        from ptt_client import PTTClient
        from graph_writer import GraphWriter

        client = PTTClient()
        writer = GraphWriter()

        try:
            # 登入 PTT
            if not client.login():
                return jsonify({
                    'success': False,
                    'error': 'PTT 登入失敗'
                }), 500

            # 取得文章最新內容
            detailed_content = client.get_post_content(post_id, board)

            if not detailed_content:
                return jsonify({
                    'success': False,
                    'error': '無法取得文章內容'
                }), 404

            comments = detailed_content.get('comments', [])

            # 更新到圖形資料庫
            new_comments_count = 0

            # 更新文章
            post_data = {
                'id': post_id,
                'properties': {
                    'post_id': post_id,
                    'title': detailed_content.get('title', ''),
                    'board': board,
                    'date': detailed_content.get('date', ''),
                    'content_length': len(detailed_content.get('content', ''))
                }
            }

            writer._upsert_post_vertex_incremental(post_data)

            # 更新推文
            for comment in comments:
                comment_author = comment.get('author', '')
                if not comment_author:
                    continue

                # 更新推文者
                commenter_data = {
                    'id': comment_author,
                    'properties': {
                        'userid': comment_author,
                        'nickname': '',
                        'ip': comment.get('ip', '')
                    }
                }
                writer._upsert_user_vertex(commenter_data)

                # 建立推文關係
                comment_result = writer._upsert_commented_edge(
                    comment_author,
                    post_id,
                    {
                        'comment_type': comment.get('type', ''),
                        'time': comment.get('time', ''),
                        'content': comment.get('content', ''),
                        'ip': comment.get('ip', '')
                    }
                )

                if comment_result.get('new', False):
                    new_comments_count += 1

            return jsonify({
                'success': True,
                'data': {
                    'post_id': post_id,
                    'board': board,
                    'total_comments': len(comments),
                    'new_comments': new_comments_count,
                    'message': f'成功重新爬取，新增 {new_comments_count} 則推文'
                },
                'query_info': {
                    'timestamp': ptt_logger.get_timestamp()
                }
            })

        finally:
            try:
                if client.is_logged_in:
                    client.logout()
                writer.close()
            except:
                pass

    except Exception as e:
        ptt_logger.error(f"重新爬取文章 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/system/crawl', methods=['POST'])
def trigger_crawl():
    """觸發爬文任務

    Request Body:
        {
            "boards": ["Test", "Gossiping"],
            "max_posts_per_board": 10
        }

    Returns:
        JSON: 爬文結果
    """
    try:
        ptt_logger.info("API 請求: 觸發爬文任務")
        
        # 取得請求參數
        data = request.get_json() or {}
        boards = data.get('boards', Config.DEFAULT_BOARDS)
        max_posts = data.get('max_posts_per_board', Config.MAX_POSTS_PER_BOARD)  # 使用配置的預設值

        # 驗證參數
        if not isinstance(boards, list) or not boards:
            return jsonify({
                'success': False,
                'error': '看板列表不能為空'
            }), 400

        if max_posts > 200:  # 限制最大文章數為200
            max_posts = 200
        
        # 檢查 PTT 配置
        ptt_config_errors = Config.validate_config()
        if ptt_config_errors:
            return jsonify({
                'success': False,
                'error': 'PTT 配置不完整',
                'details': ptt_config_errors
            }), 400
        
        # 執行爬文
        crawler = PTTCrawler()
        result = crawler.crawl_boards(boards, max_posts)
        
        if result['success']:
            # 如果有圖形資料庫連接，嘗試寫入資料
            try:
                from graph_writer import GraphWriter
                writer = GraphWriter()
                
                if writer.is_connected:
                    graph_data = crawler.generate_graph_data(result)
                    write_success = writer.write_graph_data(graph_data)
                    result['graph_write_success'] = write_success
                else:
                    result['graph_write_success'] = False
                    result['graph_write_message'] = '圖形資料庫未連接'
            except Exception as e:
                result['graph_write_success'] = False
                result['graph_write_message'] = f'圖形資料寫入錯誤: {str(e)}'
        
        # 轉換set為list以便JSON序列化
        def convert_sets_to_lists(obj):
            if isinstance(obj, dict):
                return {k: convert_sets_to_lists(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_sets_to_lists(item) for item in obj]
            elif isinstance(obj, set):
                return list(obj)
            else:
                return obj

        serializable_result = convert_sets_to_lists(result)

        return jsonify({
            'success': True,
            'data': serializable_result,
            'request_info': {
                'boards': boards,
                'max_posts_per_board': max_posts,
                'timestamp': ptt_logger.get_timestamp()
            }
        })
        
    except Exception as e:
        ptt_logger.error(f"觸發爬文任務 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/system/config', methods=['GET'])
def get_system_config():
    """取得系統配置
    
    Returns:
        JSON: 系統配置資訊
    """
    try:
        ptt_logger.info("API 請求: 取得系統配置")
        
        config_summary = Config.get_config_summary()
        ptt_errors = Config.validate_config()
        cosmos_errors = Config.validate_cosmos_config()
        
        return jsonify({
            'success': True,
            'data': {
                'configuration': config_summary,
                'validation': {
                    'ptt_valid': len(ptt_errors) == 0,
                    'cosmos_valid': len(cosmos_errors) == 0,
                    'ptt_errors': ptt_errors,
                    'cosmos_errors': cosmos_errors
                },
                'timestamp': ptt_logger.get_timestamp()
            }
        })
        
    except Exception as e:
        ptt_logger.error(f"取得系統配置 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/system/health', methods=['GET'])
def health_check():
    """健康檢查
    
    Returns:
        JSON: 健康狀態
    """
    try:
        return jsonify({
            'success': True,
            'data': {
                'status': 'healthy',
                'service': 'PTT Graph API',
                'timestamp': ptt_logger.get_timestamp()
            }
        })
        
    except Exception as e:
        ptt_logger.error(f"健康檢查 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/system/logs', methods=['GET'])
def get_recent_logs():
    """取得最近的日誌記錄
    
    Query Parameters:
        lines (int, optional): 行數限制，預設50
    
    Returns:
        JSON: 日誌記錄
    """
    try:
        lines = int(request.args.get('lines', 50))
        if lines > 200:  # 限制最大行數
            lines = 200
        
        ptt_logger.info(f"API 請求: 取得最近日誌 - {lines} 行")
        
        # 嘗試讀取日誌檔案
        import os
        from datetime import datetime
        
        log_filename = f"logs/ptt_system_{datetime.now().strftime('%Y%m%d')}.log"
        
        if os.path.exists(log_filename):
            with open(log_filename, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
            return jsonify({
                'success': True,
                'data': {
                    'logs': [line.strip() for line in recent_lines],
                    'total_lines': len(recent_lines),
                    'log_file': log_filename,
                    'timestamp': ptt_logger.get_timestamp()
                }
            })
        else:
            return jsonify({
                'success': True,
                'data': {
                    'logs': [],
                    'total_lines': 0,
                    'message': '日誌檔案不存在',
                    'timestamp': ptt_logger.get_timestamp()
                }
            })
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': f'參數格式錯誤: {str(e)}'
        }), 400
    except Exception as e:
        ptt_logger.error(f"取得日誌 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/system/database/clear', methods=['POST'])
def clear_database():
    """清理資料庫

    Request Body:
        {
            "confirm": true,
            "clear_type": "all" | "vertices" | "edges"
        }

    Returns:
        JSON: 清理結果
    """
    try:
        ptt_logger.info("API 請求: 清理資料庫")

        data = request.get_json() or {}
        confirm = data.get('confirm', False)
        clear_type = data.get('clear_type', 'all')

        if not confirm:
            return jsonify({
                'success': False,
                'error': '請確認清理操作'
            }), 400

        # 檢查 Cosmos DB 配置
        cosmos_config_errors = Config.validate_cosmos_config()
        if cosmos_config_errors:
            return jsonify({
                'success': False,
                'error': 'Cosmos DB 配置不完整',
                'details': cosmos_config_errors
            }), 400

        from graph_writer import GraphWriter
        writer = GraphWriter()

        if not writer.is_connected:
            return jsonify({
                'success': False,
                'error': '無法連接到圖形資料庫'
            }), 500

        # 執行分批清理操作
        import time

        def batch_delete(query_template, batch_size=50, delay_between_batches=1):
            """分批刪除資料以避免 RU 限制"""
            total_deleted = 0

            while True:
                try:
                    # 查詢一批資料並刪除
                    batch_query = f"{query_template}.limit({batch_size}).drop()"
                    result = writer.client.submit(batch_query).all().result()

                    # 檢查是否還有資料被刪除
                    if not result:
                        break

                    total_deleted += len(result) if result else batch_size
                    ptt_logger.info(f"已刪除 {batch_size} 個項目，總計: {total_deleted}")

                    # 在批次之間稍作停頓以避免 RU 限制
                    time.sleep(delay_between_batches)

                except Exception as e:
                    error_msg = str(e)
                    if "TooManyRequests" in error_msg or "429" in error_msg:
                        ptt_logger.warning(f"遇到 RU 限制，等待 3 秒後繼續...")
                        time.sleep(3)
                        continue
                    else:
                        raise e

            return total_deleted

        try:
            if clear_type == 'all':
                # 先刪除所有邊，再刪除所有頂點
                ptt_logger.info("開始分批清理所有邊...")
                edges_deleted = batch_delete("g.E()")

                ptt_logger.info("開始分批清理所有頂點...")
                vertices_deleted = batch_delete("g.V()")

                message = f"已清理所有資料 (頂點: {vertices_deleted}, 邊: {edges_deleted})"

            elif clear_type == 'vertices':
                # 只清理頂點
                ptt_logger.info("開始分批清理所有頂點...")
                vertices_deleted = batch_delete("g.V()")
                message = f"已清理所有頂點 ({vertices_deleted} 個)"

            elif clear_type == 'edges':
                # 只清理邊
                ptt_logger.info("開始分批清理所有邊...")
                edges_deleted = batch_delete("g.E()")
                message = f"已清理所有邊 ({edges_deleted} 個)"

            else:
                return jsonify({
                    'success': False,
                    'error': '無效的清理類型'
                }), 400

        except Exception as e:
            error_msg = str(e)
            ptt_logger.error(f"清理資料庫時發生錯誤: {error_msg}")
            return jsonify({
                'success': False,
                'error': f'清理資料庫失敗: {error_msg}'
            }), 500

        ptt_logger.info(f"資料庫清理完成: {clear_type}")

        return jsonify({
            'success': True,
            'data': {
                'message': message,
                'clear_type': clear_type,
                'timestamp': ptt_logger.get_timestamp()
            }
        })

    except Exception as e:
        ptt_logger.error(f"清理資料庫 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/system/boards', methods=['GET'])
def get_board_config():
    """取得看板配置

    Returns:
        JSON: 看板配置資訊
    """
    try:
        ptt_logger.info("API 請求: 取得看板配置")

        return jsonify({
            'success': True,
            'data': {
                'default_boards': Config.DEFAULT_BOARDS,
                'max_posts_per_board': Config.MAX_POSTS_PER_BOARD,
                'crawl_interval_hours': Config.CRAWL_INTERVAL_HOURS
            },
            'query_info': {
                'timestamp': ptt_logger.get_timestamp()
            }
        })

    except Exception as e:
        ptt_logger.error(f"取得看板配置 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/system/boards', methods=['POST'])
def update_board_config():
    """更新看板配置

    Request Body:
        {
            "default_boards": ["Test", "Gossiping"],
            "max_posts_per_board": 50,
            "crawl_interval_hours": 24
        }

    Returns:
        JSON: 更新結果
    """
    try:
        ptt_logger.info("API 請求: 更新看板配置")

        data = request.get_json() or {}

        # 驗證參數
        if 'default_boards' in data:
            boards = data['default_boards']
            if not isinstance(boards, list) or not boards:
                return jsonify({
                    'success': False,
                    'error': '看板列表必須是非空的陣列'
                }), 400

        if 'max_posts_per_board' in data:
            max_posts = data['max_posts_per_board']
            if not isinstance(max_posts, int) or max_posts < 1 or max_posts > 500:
                return jsonify({
                    'success': False,
                    'error': '每板最大文章數必須在 1-500 之間'
                }), 400

        if 'crawl_interval_hours' in data:
            interval = data['crawl_interval_hours']
            if not isinstance(interval, int) or interval < 1 or interval > 168:  # 最大一週
                return jsonify({
                    'success': False,
                    'error': '爬文間隔必須在 1-168 小時之間'
                }), 400

        # 注意：這裡只是返回配置，實際的環境變數更新需要重啟應用程式
        # 在生產環境中，這些配置應該存儲在資料庫或配置服務中

        return jsonify({
            'success': True,
            'data': {
                'message': '配置已接收（需要重啟應用程式以生效）',
                'received_config': data,
                'current_config': {
                    'default_boards': Config.DEFAULT_BOARDS,
                    'max_posts_per_board': Config.MAX_POSTS_PER_BOARD,
                    'crawl_interval_hours': Config.CRAWL_INTERVAL_HOURS
                },
                'timestamp': ptt_logger.get_timestamp()
            }
        })

    except Exception as e:
        ptt_logger.error(f"更新看板配置 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/system/scheduler/status', methods=['GET'])
def get_scheduler_status():
    """取得排程器狀態

    Returns:
        JSON: 排程器狀態資訊
    """
    try:
        ptt_logger.info("API 請求: 取得排程器狀態")

        from scheduler import PTTScheduler
        scheduler = PTTScheduler()
        status = scheduler.get_status()

        return jsonify({
            'success': True,
            'data': status,
            'query_info': {
                'timestamp': ptt_logger.get_timestamp()
            }
        })

    except Exception as e:
        ptt_logger.error(f"取得排程器狀態 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/system/scheduler/start', methods=['POST'])
def start_scheduler():
    """啟動排程器

    Returns:
        JSON: 啟動結果
    """
    try:
        ptt_logger.info("API 請求: 啟動排程器")

        from scheduler import PTTScheduler
        scheduler = PTTScheduler()

        if scheduler.get_status()['is_running']:
            return jsonify({
                'success': False,
                'error': '排程器已在運行中'
            }), 400

        scheduler.start()

        return jsonify({
            'success': True,
            'data': {
                'message': '排程器已啟動',
                'status': scheduler.get_status(),
                'timestamp': ptt_logger.get_timestamp()
            }
        })

    except Exception as e:
        ptt_logger.error(f"啟動排程器 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/system/scheduler/stop', methods=['POST'])
def stop_scheduler():
    """停止排程器

    Returns:
        JSON: 停止結果
    """
    try:
        ptt_logger.info("API 請求: 停止排程器")

        from scheduler import PTTScheduler
        scheduler = PTTScheduler()

        if not scheduler.get_status()['is_running']:
            return jsonify({
                'success': False,
                'error': '排程器未在運行'
            }), 400

        scheduler.stop()

        return jsonify({
            'success': True,
            'data': {
                'message': '排程器已停止',
                'status': scheduler.get_status(),
                'timestamp': ptt_logger.get_timestamp()
            }
        })

    except Exception as e:
        ptt_logger.error(f"停止排程器 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/system/database/stats', methods=['GET'])
def get_database_stats():
    """取得資料庫統計資訊

    Returns:
        JSON: 資料庫統計
    """
    try:
        ptt_logger.info("API 請求: 取得資料庫統計")

        from graph_writer import GraphWriter
        writer = GraphWriter()

        if not writer.is_connected:
            return jsonify({
                'success': False,
                'error': '無法連接到圖形資料庫'
            }), 500

        stats = writer.get_graph_stats()

        return jsonify({
            'success': True,
            'data': stats,
            'query_info': {
                'timestamp': ptt_logger.get_timestamp()
            }
        })

    except Exception as e:
        ptt_logger.error(f"取得資料庫統計 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

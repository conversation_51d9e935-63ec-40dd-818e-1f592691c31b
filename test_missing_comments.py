#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試推文遺漏問題
檢查特定文章的推文是否完整
"""

import sys
import time
from ptt_client import PTTClient
from logger import ptt_logger

def test_specific_post_comments():
    """測試特定文章的推文完整性"""
    print("=" * 60)
    print("測試推文遺漏問題")
    print("=" * 60)
    
    client = PTTClient()
    
    try:
        # 登入 PTT
        if not client.login():
            print("❌ PTT 登入失敗")
            return False
        
        print("✅ PTT 登入成功")
        
        # 測試 stock 看板的最新文章
        print("\n📋 取得 stock 看板最新文章...")
        posts = client.get_board_posts('stock', max_posts=10)
        
        if not posts:
            print("❌ 無法取得文章列表")
            return False
        
        print(f"✅ 取得 {len(posts)} 篇文章")
        
        # 尋找 moneywinner2 的文章
        target_post = None
        for post in posts:
            if post.get('author') == 'moneywinner2':
                target_post = post
                break
        
        if not target_post:
            print("⚠️ 未找到 moneywinner2 的文章，測試第一篇文章")
            target_post = posts[0]
        
        print(f"\n🎯 測試文章: {target_post.get('title', 'N/A')}")
        print(f"   作者: {target_post.get('author', 'N/A')}")
        print(f"   AID: {target_post.get('aid', 'N/A')}")
        
        # 取得文章詳細內容（包含推文）
        print("\n💬 取得文章推文...")
        detailed_content = client.get_post_content(
            target_post['aid'], 
            'stock'
        )
        
        if not detailed_content:
            print("❌ 無法取得文章詳細內容")
            return False
        
        comments = detailed_content.get('comments', [])
        print(f"✅ 取得 {len(comments)} 則推文")
        
        if comments:
            print("\n📝 推文列表:")
            for i, comment in enumerate(comments, 1):
                comment_type = comment.get('type', '?')
                author = comment.get('author', 'N/A')
                content = comment.get('content', 'N/A')
                time_str = comment.get('time', 'N/A')
                
                print(f"  {i:2d}. [{comment_type}] {author}: {content[:50]}... ({time_str})")
        else:
            print("📭 此文章沒有推文")
        
        # 等待一段時間後重新檢查
        print(f"\n⏰ 等待 10 秒後重新檢查推文...")
        time.sleep(10)
        
        print("🔄 重新取得推文...")
        detailed_content_2 = client.get_post_content(
            target_post['aid'], 
            'stock'
        )
        
        if detailed_content_2:
            comments_2 = detailed_content_2.get('comments', [])
            print(f"✅ 重新取得 {len(comments_2)} 則推文")
            
            # 比較推文數量
            if len(comments_2) > len(comments):
                print(f"🆕 發現新推文！增加了 {len(comments_2) - len(comments)} 則")
                
                # 顯示新推文
                new_comments = comments_2[len(comments):]
                for i, comment in enumerate(new_comments, len(comments) + 1):
                    comment_type = comment.get('type', '?')
                    author = comment.get('author', 'N/A')
                    content = comment.get('content', 'N/A')
                    time_str = comment.get('time', 'N/A')
                    
                    print(f"  新 {i:2d}. [{comment_type}] {author}: {content[:50]}... ({time_str})")
            elif len(comments_2) == len(comments):
                print("📊 推文數量沒有變化")
            else:
                print("⚠️ 推文數量減少了？這很奇怪...")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 登出
        try:
            if client.is_logged_in:
                client.logout()
                print("\n✅ 已登出 PTT")
        except:
            pass

def test_comment_timing():
    """測試推文時機問題"""
    print("\n" + "=" * 60)
    print("推文時機分析")
    print("=" * 60)
    
    print("💡 可能的推文遺漏原因:")
    print("1. 爬蟲執行時間 vs 推文時間")
    print("2. 系統沒有增量更新機制")
    print("3. 推文在爬蟲之後才發生")
    print("4. PyPtt 推文抓取有延遲")
    
    print("\n🔧 建議解決方案:")
    print("1. 實作增量推文更新")
    print("2. 定期重新爬取熱門文章")
    print("3. 提供手動重新爬取功能")
    print("4. 監控推文數量變化")

if __name__ == "__main__":
    success = test_specific_post_comments()
    test_comment_timing()
    
    if success:
        print("\n🎉 推文測試完成")
    else:
        print("\n❌ 推文測試失敗")

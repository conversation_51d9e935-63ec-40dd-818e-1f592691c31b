<template>
  <div id="app">
    <!-- 導航列 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
      <div class="container-fluid">
        <a class="navbar-brand fw-bold" href="#">
          <i class="bi bi-diagram-3"></i> PTT 圖形分析系統
        </a>
        <div class="navbar-nav ms-auto">
          <button class="btn btn-outline-light btn-sm" @click="activeTab = 'dashboard'" :class="{ active: activeTab === 'dashboard' }">
            <i class="bi bi-speedometer2"></i> 儀表板
          </button>
          <button class="btn btn-outline-light btn-sm ms-2" @click="activeTab = 'graph-query'" :class="{ active: activeTab === 'graph-query' }">
            <i class="bi bi-diagram-3"></i> 圖形化分析
          </button>
          <button class="btn btn-outline-light btn-sm ms-2" @click="activeTab = 'query'" :class="{ active: activeTab === 'query' }">
            <i class="bi bi-search"></i> 基本查詢
          </button>
          <button class="btn btn-outline-light btn-sm ms-2" @click="activeTab = 'posts'" :class="{ active: activeTab === 'posts' }">
            <i class="bi bi-file-text"></i> 文章列表
          </button>
          <button class="btn btn-outline-light btn-sm ms-2" @click="activeTab = 'admin'" :class="{ active: activeTab === 'admin' }">
            <i class="bi bi-gear"></i> 系統管理
          </button>
        </div>
      </div>
    </nav>

    <!-- 主要內容 -->
    <div class="container-fluid mt-3">
      <!-- 儀表板 -->
      <div v-if="activeTab === 'dashboard'" class="dashboard">
        <div class="row g-3">
          <!-- 系統狀態卡片 -->
          <div class="col-md-6 col-lg-3">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body text-center">
                <div class="display-6 text-primary mb-2">
                  <i class="bi bi-server"></i>
                </div>
                <h6 class="card-title">系統狀態</h6>
                <p class="card-text" :class="systemHealthy ? 'text-success' : 'text-danger'">
                  {{ systemHealthy ? '正常運行' : '異常' }}
                </p>
                <button class="btn btn-outline-primary btn-sm" @click="loadDashboardData">
                  <i class="bi bi-arrow-clockwise"></i> 重新整理
                </button>
              </div>
            </div>
          </div>

          <!-- 使用者統計 -->
          <div class="col-md-6 col-lg-3">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body text-center">
                <div class="display-6 text-info mb-2">
                  <i class="bi bi-people"></i>
                </div>
                <h6 class="card-title">使用者總數</h6>
                <p class="card-text display-6 fw-bold text-info">
                  {{ stats.userCount || 0 }}
                </p>
              </div>
            </div>
          </div>

          <!-- IP 統計 -->
          <div class="col-md-6 col-lg-3">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body text-center">
                <div class="display-6 text-warning mb-2">
                  <i class="bi bi-router"></i>
                </div>
                <h6 class="card-title">IP 位址總數</h6>
                <p class="card-text display-6 fw-bold text-warning">
                  {{ stats.ipCount || 0 }}
                </p>
              </div>
            </div>
          </div>

          <!-- 關聯統計 -->
          <div class="col-md-6 col-lg-3">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body text-center">
                <div class="display-6 text-success mb-2">
                  <i class="bi bi-diagram-2"></i>
                </div>
                <h6 class="card-title">關聯總數</h6>
                <p class="card-text display-6 fw-bold text-success">
                  {{ stats.relationCount || 0 }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="row mt-4">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-light">
                <h5 class="mb-0"><i class="bi bi-lightning"></i> 快速查詢</h5>
              </div>
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-md-6">
                    <div class="input-group">
                      <span class="input-group-text"><i class="bi bi-person"></i></span>
                      <input type="text" class="form-control" placeholder="輸入使用者名稱" v-model="quickQuery.user">
                      <button class="btn btn-primary" @click="quickUserQuery" :disabled="!quickQuery.user.trim()">
                        查詢使用者
                      </button>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="input-group">
                      <span class="input-group-text"><i class="bi bi-router"></i></span>
                      <input type="text" class="form-control" placeholder="輸入 IP 位址" v-model="quickQuery.ip">
                      <button class="btn btn-success" @click="quickIpQuery" :disabled="!quickQuery.ip.trim()">
                        查詢 IP
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 圖形化分析 -->
      <div v-if="activeTab === 'graph-query'" class="graph-query-section">
        <GraphQueryPage />
      </div>

      <!-- 基本查詢 -->
      <div v-if="activeTab === 'query'" class="query-section">
        <div class="row">
          <!-- 查詢面板 -->
          <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="bi bi-search"></i> 圖形查詢</h5>
              </div>
              <div class="card-body">
                <!-- 查詢類型選擇 -->
                <div class="mb-3">
                  <label class="form-label">查詢類型</label>
                  <select class="form-select" v-model="queryType">
                    <option value="user-network-simple">使用者關聯查詢 (簡化版)</option>
                    <option value="user-links">使用者關聯查詢 (完整版)</option>
                    <option value="ip-users">IP 使用者查詢</option>
                    <option value="user-ip-analysis">使用者 IP 分析</option>
                  </select>
                </div>

                <!-- 使用者查詢 -->
                <div v-if="queryType === 'user-network-simple' || queryType === 'user-links'" class="mb-3">
                  <label class="form-label">使用者名稱</label>
                  <input type="text" class="form-control" v-model="queryParams.username" placeholder="輸入使用者名稱">
                  <div class="mt-2">
                    <label class="form-label">查詢深度</label>
                    <select class="form-select" v-model="queryParams.maxDepth">
                      <option value="1">1 層</option>
                      <option value="2">2 層</option>
                      <option value="3">3 層</option>
                    </select>
                  </div>
                </div>

                <!-- IP 查詢 -->
                <div v-if="queryType === 'ip-users'" class="mb-3">
                  <label class="form-label">IP 位址</label>
                  <input type="text" class="form-control" v-model="queryParams.ip" placeholder="例如: ***********">
                </div>

                <!-- 使用者 IP 分析 -->
                <div v-if="queryType === 'user-ip-analysis'" class="mb-3">
                  <label class="form-label">使用者名稱</label>
                  <input type="text" class="form-control" v-model="queryParams.username" placeholder="輸入使用者名稱">
                </div>

                <!-- 查詢按鈕 -->
                <button class="btn btn-primary w-100" @click="executeQuery" :disabled="isQuerying">
                  <span v-if="!isQuerying">
                    <i class="bi bi-play-circle"></i> 執行查詢
                  </span>
                  <span v-else>
                    <span class="spinner-border spinner-border-sm me-2"></span>
                    查詢中...
                  </span>
                </button>

                <!-- 清除結果 -->
                <button v-if="queryResult" class="btn btn-outline-secondary w-100 mt-2" @click="clearQuery">
                  <i class="bi bi-x-circle"></i> 清除結果
                </button>
              </div>
            </div>
          </div>

          <!-- 結果顯示 -->
          <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-light">
                <h5 class="mb-0"><i class="bi bi-graph-up"></i> 查詢結果</h5>
              </div>
              <div class="card-body">
                <!-- 無結果提示 -->
                <div v-if="!queryResult" class="text-center text-muted py-5">
                  <i class="bi bi-search display-1"></i>
                  <p class="mt-3">請在左側選擇查詢類型並執行查詢</p>
                </div>

                <!-- 查詢結果 -->
                <div v-if="queryResult" class="query-results">
                  <!-- 結果摘要 -->
                  <div class="alert alert-info">
                    <strong>查詢完成</strong> - {{ queryResult.query_info?.timestamp }}
                  </div>

                  <!-- 結果內容 -->
                  <div class="result-content">
                    <pre class="bg-light p-3 rounded">{{ JSON.stringify(queryResult.data, null, 2) }}</pre>
                  </div>
                </div>

                <!-- 錯誤訊息 -->
                <div v-if="queryError" class="alert alert-danger">
                  <strong>查詢錯誤:</strong> {{ queryError }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 文章列表 -->
      <div v-if="activeTab === 'posts'" class="posts-section">
        <div class="row">
          <!-- 篩選面板 -->
          <div class="col-lg-3">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="bi bi-funnel"></i> 篩選條件</h5>
              </div>
              <div class="card-body">
                <!-- 看板篩選 -->
                <div class="mb-3">
                  <label class="form-label">看板</label>
                  <input type="text" class="form-control" v-model="postsFilter.board" placeholder="輸入看板名稱 (可選)">
                </div>

                <!-- 每頁顯示數量 -->
                <div class="mb-3">
                  <label class="form-label">每頁顯示</label>
                  <select class="form-select" v-model="postsFilter.limit">
                    <option value="10">10 篇</option>
                    <option value="20">20 篇</option>
                    <option value="50">50 篇</option>
                    <option value="100">100 篇</option>
                  </select>
                </div>

                <!-- 查詢按鈕 -->
                <button class="btn btn-success w-100" @click="loadPosts" :disabled="isLoadingPosts">
                  <span v-if="!isLoadingPosts">
                    <i class="bi bi-search"></i> 查詢文章
                  </span>
                  <span v-else>
                    <span class="spinner-border spinner-border-sm me-2"></span>
                    載入中...
                  </span>
                </button>

                <!-- 重置按鈕 -->
                <button class="btn btn-outline-secondary w-100 mt-2" @click="resetPostsFilter">
                  <i class="bi bi-arrow-clockwise"></i> 重置篩選
                </button>
              </div>
            </div>
          </div>

          <!-- 文章列表 -->
          <div class="col-lg-9">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-file-text"></i> 文章列表</h5>
                <span v-if="postsData" class="badge bg-primary">
                  共 {{ postsData.total_count }} 篇文章
                </span>
              </div>
              <div class="card-body">
                <!-- 無結果提示 -->
                <div v-if="!postsData && !isLoadingPosts" class="text-center text-muted py-5">
                  <i class="bi bi-file-text display-1"></i>
                  <p class="mt-3">請點擊左側「查詢文章」按鈕載入文章列表</p>
                </div>

                <!-- 載入中 -->
                <div v-if="isLoadingPosts" class="text-center py-5">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">載入中...</span>
                  </div>
                  <p class="mt-3 text-muted">正在載入文章資料...</p>
                </div>

                <!-- 文章列表 -->
                <div v-if="postsData && !isLoadingPosts" class="posts-list">
                  <!-- 分頁控制 (上方) -->
                  <div v-if="postsData.total_count > postsFilter.limit" class="d-flex justify-content-between align-items-center mb-3">
                    <div class="text-muted">
                      顯示第 {{ currentOffset + 1 }} - {{ Math.min(currentOffset + parseInt(postsFilter.limit), postsData.total_count) }} 篇，
                      共 {{ postsData.total_count }} 篇
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-outline-primary btn-sm" @click="previousPage" :disabled="currentOffset === 0">
                        <i class="bi bi-chevron-left"></i> 上一頁
                      </button>
                      <button class="btn btn-outline-primary btn-sm" @click="nextPage" :disabled="!postsData.has_more">
                        下一頁 <i class="bi bi-chevron-right"></i>
                      </button>
                    </div>
                  </div>

                  <!-- 文章項目 -->
                  <div v-for="post in postsData.posts" :key="post.post_id" class="post-item mb-4 p-3 border rounded">
                    <!-- 文章標題和基本資訊 -->
                    <div class="row">
                      <div class="col-md-8">
                        <h6 class="post-title mb-2">{{ post.title || '無標題' }}</h6>
                        <div class="post-meta text-muted small">
                          <span><i class="bi bi-person"></i> {{ post.author.userid || '未知' }}</span>
                          <span v-if="post.author.nickname" class="ms-2">({{ post.author.nickname }})</span>
                          <span class="ms-3"><i class="bi bi-calendar"></i> {{ post.date || '未知' }}</span>
                          <span class="ms-3"><i class="bi bi-tag"></i> {{ post.board || '未知' }}</span>
                        </div>
                      </div>
                      <div class="col-md-4 text-end">
                        <div class="post-stats">
                          <span class="badge bg-info me-1">{{ post.stats.total_comments }} 推文</span>
                          <span class="badge bg-success me-1">{{ post.stats.push_count }} 推</span>
                          <span class="badge bg-danger me-1">{{ post.stats.boo_count }} 噓</span>
                        </div>
                        <div class="text-muted small mt-1">
                          {{ post.stats.unique_commenters }} 位使用者參與
                        </div>
                      </div>
                    </div>

                    <!-- 發文者IP資訊 -->
                    <div v-if="post.author.ip" class="author-ip mt-2">
                      <small class="text-warning">
                        <i class="bi bi-router"></i> 發文IP: {{ post.author.ip }}
                      </small>
                    </div>

                    <!-- 推文者列表 (摺疊) -->
                    <div v-if="post.commenters.length > 0" class="commenters-section mt-3">
                      <div class="d-flex gap-2">
                        <button class="btn btn-outline-secondary btn-sm" @click="toggleCommenters(post.post_id)">
                          <i class="bi bi-chat-dots"></i>
                          {{ expandedPosts.includes(post.post_id) ? '隱藏' : '顯示' }} 推文詳情
                          ({{ post.commenters.length }} 則)
                        </button>
                        <button class="btn btn-outline-warning btn-sm" @click="recrawlPost(post.post_id, post.board)" :disabled="isRecrawling">
                          <i class="bi bi-arrow-clockwise"></i>
                          {{ isRecrawling ? '更新中...' : '重新爬取推文' }}
                        </button>
                      </div>

                      <div v-if="expandedPosts.includes(post.post_id)" class="commenters-list mt-2">
                        <div class="table-responsive">
                          <table class="table table-sm">
                            <thead>
                              <tr>
                                <th>使用者</th>
                                <th>類型</th>
                                <th>時間</th>
                                <th>內容</th>
                                <th>IP</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr v-for="commenter in post.commenters.slice(0, 10)" :key="`${post.post_id}-${commenter.userid}-${commenter.time}`">
                                <td>
                                  <span class="fw-bold">{{ commenter.userid }}</span>
                                  <small v-if="commenter.nickname" class="text-muted d-block">({{ commenter.nickname }})</small>
                                </td>
                                <td>
                                  <span class="badge" :class="{
                                    'bg-success': commenter.comment_type === '推',
                                    'bg-danger': commenter.comment_type === '噓',
                                    'bg-secondary': commenter.comment_type === '→'
                                  }">
                                    {{ commenter.comment_type }}
                                  </span>
                                </td>
                                <td class="small text-muted">{{ commenter.time }}</td>
                                <td class="small">{{ commenter.content.substring(0, 50) }}{{ commenter.content.length > 50 ? '...' : '' }}</td>
                                <td class="small text-warning">{{ commenter.ip || '-' }}</td>
                              </tr>
                            </tbody>
                          </table>
                          <div v-if="post.commenters.length > 10" class="text-muted small text-center">
                            還有 {{ post.commenters.length - 10 }} 則推文...
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 分頁控制 (下方) -->
                  <div v-if="postsData.total_count > postsFilter.limit" class="d-flex justify-content-center mt-4">
                    <div class="btn-group">
                      <button class="btn btn-outline-primary" @click="previousPage" :disabled="currentOffset === 0">
                        <i class="bi bi-chevron-left"></i> 上一頁
                      </button>
                      <button class="btn btn-outline-primary" @click="nextPage" :disabled="!postsData.has_more">
                        下一頁 <i class="bi bi-chevron-right"></i>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 錯誤訊息 -->
                <div v-if="postsError" class="alert alert-danger">
                  <strong>載入錯誤:</strong> {{ postsError }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 系統管理 -->
      <div v-if="activeTab === 'admin'" class="admin-section">
        <div class="row">
          <!-- 資料庫管理 -->
          <div class="col-lg-6">
            <div class="card border-0 shadow-sm mb-4">
              <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="bi bi-database"></i> 資料庫管理</h5>
              </div>
              <div class="card-body">
                <!-- 資料庫統計 -->
                <div v-if="dbStats" class="mb-3">
                  <h6>資料庫統計</h6>
                  <div class="row text-center">
                    <div class="col-4">
                      <div class="border rounded p-2">
                        <div class="h5 text-primary mb-1">{{ dbStats.vertices?.users || 0 }}</div>
                        <small class="text-muted">使用者</small>
                      </div>
                    </div>
                    <div class="col-4">
                      <div class="border rounded p-2">
                        <div class="h5 text-info mb-1">{{ dbStats.vertices?.posts || 0 }}</div>
                        <small class="text-muted">文章</small>
                      </div>
                    </div>
                    <div class="col-4">
                      <div class="border rounded p-2">
                        <div class="h5 text-warning mb-1">{{ dbStats.vertices?.ips || 0 }}</div>
                        <small class="text-muted">IP位址</small>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 清理資料庫 -->
                <div class="alert alert-warning">
                  <strong>⚠️ 危險操作</strong><br>
                  清理資料庫將永久刪除所有資料，此操作無法復原！
                </div>

                <div class="mb-3">
                  <label class="form-label">清理類型</label>
                  <select class="form-select" v-model="clearType">
                    <option value="all">清理所有資料</option>
                    <option value="vertices">只清理頂點</option>
                    <option value="edges">只清理邊</option>
                  </select>
                </div>

                <div class="mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" v-model="confirmClear" id="confirmClear">
                    <label class="form-check-label text-danger" for="confirmClear">
                      我確認要執行此危險操作
                    </label>
                  </div>
                </div>

                <button class="btn btn-danger w-100" @click="clearDatabase" :disabled="!confirmClear || isClearingDb">
                  <span v-if="!isClearingDb">
                    <i class="bi bi-trash"></i> 清理資料庫
                  </span>
                  <span v-else>
                    <span class="spinner-border spinner-border-sm me-2"></span>
                    清理中...
                  </span>
                </button>

                <button class="btn btn-outline-primary w-100 mt-2" @click="loadDbStats">
                  <i class="bi bi-arrow-clockwise"></i> 重新整理統計
                </button>
              </div>
            </div>
          </div>

          <!-- 爬文管理 -->
          <div class="col-lg-6">
            <div class="card border-0 shadow-sm mb-4">
              <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="bi bi-robot"></i> 爬文管理</h5>
              </div>
              <div class="card-body">
                <!-- 排程器狀態 -->
                <div v-if="schedulerStatus" class="mb-3">
                  <h6>排程器狀態</h6>
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>運行狀態:</span>
                    <span :class="schedulerStatus.is_running ? 'badge bg-success' : 'badge bg-secondary'">
                      {{ schedulerStatus.is_running ? '運行中' : '已停止' }}
                    </span>
                  </div>
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>上次爬文:</span>
                    <small class="text-muted">{{ schedulerStatus.last_crawl_time || '無' }}</small>
                  </div>
                  <div class="d-flex justify-content-between align-items-center">
                    <span>總執行次數:</span>
                    <span class="badge bg-info">{{ schedulerStatus.total_results || 0 }}</span>
                  </div>
                </div>

                <!-- 排程器控制 -->
                <div class="mb-3">
                  <div class="btn-group w-100">
                    <button class="btn btn-success" @click="startScheduler" :disabled="schedulerStatus?.is_running || isSchedulerLoading">
                      <i class="bi bi-play"></i> 啟動排程
                    </button>
                    <button class="btn btn-warning" @click="stopScheduler" :disabled="!schedulerStatus?.is_running || isSchedulerLoading">
                      <i class="bi bi-stop"></i> 停止排程
                    </button>
                  </div>
                </div>

                <!-- 手動爬文 -->
                <div class="mb-3">
                  <label class="form-label">看板列表</label>
                  <input type="text" class="form-control" v-model="manualCrawlBoards" placeholder="例如: Test,Stock">
                  <div class="form-text">用逗號分隔多個看板</div>
                </div>

                <div class="mb-3">
                  <label class="form-label">每板文章數</label>
                  <select class="form-select" v-model="manualCrawlPosts">
                    <option value="10">10 篇</option>
                    <option value="20">20 篇</option>
                    <option value="50">50 篇</option>
                    <option value="100">100 篇</option>
                  </select>
                </div>

                <button class="btn btn-primary w-100" @click="triggerManualCrawl" :disabled="isManualCrawling">
                  <span v-if="!isManualCrawling">
                    <i class="bi bi-download"></i> 手動爬文
                  </span>
                  <span v-else>
                    <span class="spinner-border spinner-border-sm me-2"></span>
                    爬文中...
                  </span>
                </button>

                <button class="btn btn-outline-secondary w-100 mt-2" @click="loadSchedulerStatus">
                  <i class="bi bi-arrow-clockwise"></i> 重新整理狀態
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 看板配置 -->
        <div class="row">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="bi bi-list-ul"></i> 看板配置</h5>
              </div>
              <div class="card-body">
                <div v-if="boardConfig" class="row">
                  <div class="col-md-4">
                    <div class="mb-3">
                      <label class="form-label">預設看板列表</label>
                      <textarea class="form-control" rows="3" v-model="editBoardConfig.default_boards_text"
                                placeholder="每行一個看板名稱"></textarea>
                      <div class="form-text">目前: {{ boardConfig.default_boards?.join(', ') }}</div>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="mb-3">
                      <label class="form-label">每板最大文章數</label>
                      <input type="number" class="form-control" v-model="editBoardConfig.max_posts_per_board"
                             min="1" max="500">
                      <div class="form-text">目前: {{ boardConfig.max_posts_per_board }}</div>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="mb-3">
                      <label class="form-label">爬文間隔 (小時)</label>
                      <input type="number" class="form-control" v-model="editBoardConfig.crawl_interval_hours"
                             min="1" max="168">
                      <div class="form-text">目前: {{ boardConfig.crawl_interval_hours }} 小時</div>
                    </div>
                  </div>
                </div>

                <div class="alert alert-info">
                  <strong>注意:</strong> 配置更新後需要重啟應用程式才能生效。
                </div>

                <div class="btn-group">
                  <button class="btn btn-info" @click="updateBoardConfig" :disabled="isBoardConfigLoading">
                    <span v-if="!isBoardConfigLoading">
                      <i class="bi bi-save"></i> 更新配置
                    </span>
                    <span v-else>
                      <span class="spinner-border spinner-border-sm me-2"></span>
                      更新中...
                    </span>
                  </button>
                  <button class="btn btn-outline-secondary" @click="loadBoardConfig">
                    <i class="bi bi-arrow-clockwise"></i> 重新載入
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作結果提示 -->
        <div v-if="adminMessage" class="alert mt-3" :class="adminMessage.type === 'success' ? 'alert-success' : 'alert-danger'">
          {{ adminMessage.text }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import GraphQueryPage from './components/GraphQueryPage.vue'

// 響應式資料
const activeTab = ref('dashboard')
const systemHealthy = ref(true)
const stats = ref({
  userCount: 0,
  ipCount: 0,
  relationCount: 0
})

// 快速查詢
const quickQuery = ref({
  user: '',
  ip: ''
})

// 圖形查詢
const queryType = ref('user-network-simple')
const queryParams = ref({
  username: '',
  ip: '',
  maxDepth: '2'
})
const queryResult = ref(null)
const queryError = ref(null)
const isQuerying = ref(false)

// 文章列表
const postsFilter = ref({
  board: '',
  limit: '20'
})
const postsData = ref(null)
const postsError = ref(null)
const isLoadingPosts = ref(false)
const currentOffset = ref(0)
const expandedPosts = ref([])
const isRecrawling = ref(false)

// 系統管理
const dbStats = ref(null)
const clearType = ref('all')
const confirmClear = ref(false)
const isClearingDb = ref(false)
const schedulerStatus = ref(null)
const isSchedulerLoading = ref(false)
const manualCrawlBoards = ref('Test')
const manualCrawlPosts = ref('50')
const isManualCrawling = ref(false)
const boardConfig = ref(null)
const editBoardConfig = ref({
  default_boards_text: '',
  max_posts_per_board: 50,
  crawl_interval_hours: 24
})
const isBoardConfigLoading = ref(false)
const adminMessage = ref(null)

// 載入儀表板資料
const loadDashboardData = async () => {
  try {
    // 載入系統狀態
    const statusResponse = await fetch('/api/system/status')
    const statusData = await statusResponse.json()
    systemHealthy.value = statusData.success

    // 載入統計資料
    const statsResponse = await fetch('/api/graph-stats')
    const statsData = await statsResponse.json()
    if (statsData.success) {
      stats.value = {
        userCount: statsData.data.user_count || 0,
        ipCount: statsData.data.ip_count || 0,
        relationCount: statsData.data.edge_count || 0
      }
    }
  } catch (error) {
    console.error('載入儀表板資料失敗:', error)
    systemHealthy.value = false
  }
}

// 快速使用者查詢
const quickUserQuery = () => {
  queryType.value = 'user-network-simple'
  queryParams.value.username = quickQuery.value.user
  activeTab.value = 'query'
  executeQuery()
}

// 快速 IP 查詢
const quickIpQuery = () => {
  queryType.value = 'ip-users'
  queryParams.value.ip = quickQuery.value.ip
  activeTab.value = 'query'
  executeQuery()
}

// 執行查詢
const executeQuery = async () => {
  if (isQuerying.value) return

  isQuerying.value = true
  queryResult.value = null
  queryError.value = null

  try {
    let url = ''
    let params = new URLSearchParams()

    switch (queryType.value) {
      case 'user-network-simple':
        url = '/api/user-network-simple'
        params.append('username', queryParams.value.username)
        params.append('max_depth', queryParams.value.maxDepth)
        break
      case 'user-links':
        url = '/api/user-links'
        params.append('username', queryParams.value.username)
        params.append('max_depth', queryParams.value.maxDepth)
        break
      case 'ip-users':
        url = '/api/ip-users'
        params.append('ip', queryParams.value.ip)
        break
      case 'user-ip-analysis':
        url = '/api/user-ip-analysis'
        params.append('username', queryParams.value.username)
        break
    }

    const response = await fetch(`${url}?${params}`)
    const data = await response.json()

    if (data.success) {
      queryResult.value = data
    } else {
      queryError.value = data.error || '查詢失敗'
    }
  } catch (error) {
    queryError.value = `請求失敗: ${error.message}`
  } finally {
    isQuerying.value = false
  }
}

// 清除查詢結果
const clearQuery = () => {
  queryResult.value = null
  queryError.value = null
}

// 載入文章列表
const loadPosts = async () => {
  if (isLoadingPosts.value) return

  isLoadingPosts.value = true
  postsError.value = null

  try {
    let params = new URLSearchParams()
    params.append('limit', postsFilter.value.limit)
    params.append('offset', currentOffset.value.toString())

    if (postsFilter.value.board.trim()) {
      params.append('board', postsFilter.value.board.trim())
    }

    const response = await fetch(`/api/posts?${params}`)
    const data = await response.json()

    if (data.success) {
      postsData.value = data.data
    } else {
      postsError.value = data.error || '載入文章失敗'
    }
  } catch (error) {
    postsError.value = `請求失敗: ${error.message}`
  } finally {
    isLoadingPosts.value = false
  }
}

// 重置文章篩選
const resetPostsFilter = () => {
  postsFilter.value = {
    board: '',
    limit: '20'
  }
  currentOffset.value = 0
  postsData.value = null
  postsError.value = null
  expandedPosts.value = []
}

// 上一頁
const previousPage = () => {
  if (currentOffset.value > 0) {
    currentOffset.value = Math.max(0, currentOffset.value - parseInt(postsFilter.value.limit))
    loadPosts()
  }
}

// 下一頁
const nextPage = () => {
  if (postsData.value && postsData.value.has_more) {
    currentOffset.value += parseInt(postsFilter.value.limit)
    loadPosts()
  }
}

// 切換推文詳情顯示
const toggleCommenters = (postId) => {
  const index = expandedPosts.value.indexOf(postId)
  if (index > -1) {
    expandedPosts.value.splice(index, 1)
  } else {
    expandedPosts.value.push(postId)
  }
}

// 載入資料庫統計
const loadDbStats = async () => {
  try {
    const response = await fetch('/api/system/database/stats')
    const data = await response.json()
    if (data.success) {
      dbStats.value = data.data
    }
  } catch (error) {
    console.error('載入資料庫統計失敗:', error)
  }
}

// 清理資料庫
const clearDatabase = async () => {
  if (!confirmClear.value) return

  isClearingDb.value = true
  adminMessage.value = null

  try {
    const response = await fetch('/api/system/database/clear', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        confirm: true,
        clear_type: clearType.value
      })
    })

    const data = await response.json()

    if (data.success) {
      adminMessage.value = { type: 'success', text: data.data.message }
      confirmClear.value = false
      await loadDbStats() // 重新載入統計
    } else {
      adminMessage.value = { type: 'error', text: data.error }
    }
  } catch (error) {
    adminMessage.value = { type: 'error', text: `清理失敗: ${error.message}` }
  } finally {
    isClearingDb.value = false
  }
}

// 載入排程器狀態
const loadSchedulerStatus = async () => {
  try {
    const response = await fetch('/api/system/scheduler/status')
    const data = await response.json()
    if (data.success) {
      schedulerStatus.value = data.data
    }
  } catch (error) {
    console.error('載入排程器狀態失敗:', error)
  }
}

// 啟動排程器
const startScheduler = async () => {
  isSchedulerLoading.value = true
  adminMessage.value = null

  try {
    const response = await fetch('/api/system/scheduler/start', {
      method: 'POST'
    })

    const data = await response.json()

    if (data.success) {
      adminMessage.value = { type: 'success', text: data.data.message }
      await loadSchedulerStatus()
    } else {
      adminMessage.value = { type: 'error', text: data.error }
    }
  } catch (error) {
    adminMessage.value = { type: 'error', text: `啟動失敗: ${error.message}` }
  } finally {
    isSchedulerLoading.value = false
  }
}

// 停止排程器
const stopScheduler = async () => {
  isSchedulerLoading.value = true
  adminMessage.value = null

  try {
    const response = await fetch('/api/system/scheduler/stop', {
      method: 'POST'
    })

    const data = await response.json()

    if (data.success) {
      adminMessage.value = { type: 'success', text: data.data.message }
      await loadSchedulerStatus()
    } else {
      adminMessage.value = { type: 'error', text: data.error }
    }
  } catch (error) {
    adminMessage.value = { type: 'error', text: `停止失敗: ${error.message}` }
  } finally {
    isSchedulerLoading.value = false
  }
}

// 手動觸發爬文
const triggerManualCrawl = async () => {
  if (!manualCrawlBoards.value.trim()) return

  isManualCrawling.value = true
  adminMessage.value = null

  try {
    const boards = manualCrawlBoards.value.split(',').map(b => b.trim()).filter(b => b)

    const response = await fetch('/api/system/crawl', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        boards: boards,
        max_posts_per_board: parseInt(manualCrawlPosts.value)
      })
    })

    const data = await response.json()

    if (data.success) {
      const summary = data.data.summary
      adminMessage.value = {
        type: 'success',
        text: `爬文完成！共爬取 ${summary.boards_count} 個看板，${summary.total_posts} 篇文章`
      }
      await loadDbStats() // 重新載入統計
    } else {
      adminMessage.value = { type: 'error', text: data.error }
    }
  } catch (error) {
    adminMessage.value = { type: 'error', text: `爬文失敗: ${error.message}` }
  } finally {
    isManualCrawling.value = false
  }
}

// 載入看板配置
const loadBoardConfig = async () => {
  try {
    const response = await fetch('/api/system/boards')
    const data = await response.json()
    if (data.success) {
      boardConfig.value = data.data
      editBoardConfig.value = {
        default_boards_text: data.data.default_boards.join('\n'),
        max_posts_per_board: data.data.max_posts_per_board,
        crawl_interval_hours: data.data.crawl_interval_hours
      }
    }
  } catch (error) {
    console.error('載入看板配置失敗:', error)
  }
}

// 更新看板配置
const updateBoardConfig = async () => {
  isBoardConfigLoading.value = true
  adminMessage.value = null

  try {
    const boards = editBoardConfig.value.default_boards_text
      .split('\n')
      .map(b => b.trim())
      .filter(b => b)

    const response = await fetch('/api/system/boards', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        default_boards: boards,
        max_posts_per_board: parseInt(editBoardConfig.value.max_posts_per_board),
        crawl_interval_hours: parseInt(editBoardConfig.value.crawl_interval_hours)
      })
    })

    const data = await response.json()

    if (data.success) {
      adminMessage.value = { type: 'success', text: data.data.message }
      await loadBoardConfig()
    } else {
      adminMessage.value = { type: 'error', text: data.error }
    }
  } catch (error) {
    adminMessage.value = { type: 'error', text: `更新失敗: ${error.message}` }
  } finally {
    isBoardConfigLoading.value = false
  }
}

// 監聽標籤切換
watch(activeTab, (newTab) => {
  if (newTab === 'admin') {
    loadDbStats()
    loadSchedulerStatus()
    loadBoardConfig()
  }
})

// 頁面載入時執行
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.btn.active {
  background-color: rgba(255, 255, 255, 0.2);
}

.card {
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
}

.result-content {
  max-height: 500px;
  overflow-y: auto;
}

pre {
  font-size: 0.875rem;
  max-height: 400px;
  overflow-y: auto;
}

/* 文章列表樣式 */
.post-item {
  transition: all 0.2s ease-in-out;
  background-color: #fafafa;
}

.post-item:hover {
  background-color: #f0f0f0;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.post-title {
  color: #2c3e50;
  font-weight: 600;
}

.post-meta {
  font-size: 0.85rem;
}

.post-stats .badge {
  font-size: 0.75rem;
}

.author-ip {
  padding: 0.25rem 0.5rem;
  background-color: #fff3cd;
  border-radius: 0.25rem;
  border-left: 3px solid #ffc107;
}

.commenters-list {
  background-color: #f8f9fa;
  border-radius: 0.375rem;
  padding: 1rem;
  border: 1px solid #dee2e6;
}

.commenters-list table {
  margin-bottom: 0;
}

.commenters-list th {
  background-color: #e9ecef;
  font-size: 0.8rem;
  font-weight: 600;
  border: none;
}

.commenters-list td {
  font-size: 0.85rem;
  border: none;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
}
</style>

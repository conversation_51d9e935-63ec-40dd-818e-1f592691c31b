<!doctype html>
<html lang="zh-TW">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}PTT 圖形分析系統{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Cytoscape.js for graph visualization -->
    <script src="https://unpkg.com/cytoscape@3.21.1/dist/cytoscape.min.js"></script>
    <!-- Cytoscape.js extensions -->
    <script src="https://unpkg.com/cytoscape-cose-bilkent@4.1.0/cytoscape-cose-bilkent.js"></script>
    
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        
        .config-info {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 20px 0;
        }
        
        .error-info {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 20px 0;
        }
        
        .success-info {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
        }
        
        .graph-container {
            height: 600px;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            background-color: #f8f9fa;
        }
        
        .search-form {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 0.375rem;
            margin-bottom: 20px;
        }
        
        .result-card {
            margin-bottom: 20px;
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 40px;
        }
        
        .graph-controls {
            margin-bottom: 15px;
        }
        
        .graph-legend {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 15px;
            margin-top: 15px;
        }
        
        .legend-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        
        .legend-color {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 8px;
            vertical-align: middle;
        }
        
        .user-node { background-color: #007bff; }
        .ip-node { background-color: #28a745; }
        .post-node { background-color: #ffc107; }
        
        .stats-card {
            text-align: center;
            padding: 20px;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .footer {
            margin-top: 50px;
            padding: 20px 0;
            border-top: 1px solid #dee2e6;
            background-color: #f8f9fa;
        }
        
        {% block extra_css %}{% endblock %}
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-diagram-3"></i> PTT 圖形分析系統
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="bi bi-house"></i> 首頁
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('user_analysis') }}">
                            <i class="bi bi-person-lines-fill"></i> 使用者分析
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('ip_analysis') }}">
                            <i class="bi bi-router"></i> IP 分析
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('post_analysis') }}">
                            <i class="bi bi-chat-dots"></i> 文章互動
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="systemDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-gear"></i> 系統
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('system_status') }}">系統狀態</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('crawl_management') }}">爬文管理</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/api/system/health" target="_blank">API 文件</a></li>
                        </ul>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <i class="bi bi-clock"></i> <span id="currentTime"></span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 PTT 圖形分析系統</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <a href="/api/system/health" class="text-decoration-none">API 狀態</a> |
                        <a href="{{ url_for('system_status') }}" class="text-decoration-none">系統狀態</a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Common JavaScript -->
    <script>
        // 更新當前時間
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }
        
        // 每秒更新時間
        setInterval(updateTime, 1000);
        updateTime();
        
        // 通用 API 請求函數
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('API 請求失敗:', error);
                throw error;
            }
        }
        
        // 顯示載入狀態
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = 'block';
            }
        }
        
        // 隱藏載入狀態
        function hideLoading(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = 'none';
            }
        }
        
        // 顯示錯誤訊息
        function showError(message, containerId) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle"></i> ${message}
                    </div>
                `;
            }
        }
        
        // 顯示成功訊息
        function showSuccess(message, containerId) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `
                    <div class="alert alert-success" role="alert">
                        <i class="bi bi-check-circle"></i> ${message}
                    </div>
                `;
            }
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>

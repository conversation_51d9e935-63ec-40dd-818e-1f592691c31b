#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手動重新爬取特定文章的推文
用於修正推文遺漏問題
"""

import sys
from ptt_client import PTTClient
from graph_writer import GraphWriter
from logger import ptt_logger

def recrawl_post_comments(post_id, board_name):
    """重新爬取特定文章的推文
    
    Args:
        post_id (str): 文章 ID (AID)
        board_name (str): 看板名稱
    
    Returns:
        bool: 是否成功
    """
    print(f"🔄 重新爬取文章推文: {post_id} (看板: {board_name})")
    
    client = PTTClient()
    writer = GraphWriter()
    
    try:
        # 登入 PTT
        if not client.login():
            print("❌ PTT 登入失敗")
            return False
        
        print("✅ PTT 登入成功")
        
        # 取得文章最新內容
        print("📄 取得文章最新內容...")
        detailed_content = client.get_post_content(post_id, board_name)
        
        if not detailed_content:
            print("❌ 無法取得文章內容")
            return False
        
        comments = detailed_content.get('comments', [])
        print(f"✅ 取得 {len(comments)} 則推文")
        
        # 顯示推文列表
        if comments:
            print("\n📝 最新推文列表:")
            for i, comment in enumerate(comments, 1):
                comment_type = comment.get('type', '?')
                author = comment.get('author', 'N/A')
                content = comment.get('content', 'N/A')[:30]
                time_str = comment.get('time', 'N/A')
                
                print(f"  {i:2d}. [{comment_type}] {author}: {content}... ({time_str})")
        
        # 更新到圖形資料庫
        print("\n💾 更新圖形資料庫...")
        
        # 準備文章資料
        post_data = {
            'id': post_id,
            'properties': {
                'post_id': post_id,
                'title': detailed_content.get('title', ''),
                'board': board_name,
                'date': detailed_content.get('date', ''),
                'content_length': len(detailed_content.get('content', ''))
            }
        }
        
        # 更新文章
        result = writer._upsert_post_vertex_incremental(post_data)
        if result['success']:
            print("✅ 文章資料更新成功")
        
        # 更新作者
        author = detailed_content.get('author', '')
        if author:
            author_data = {
                'id': author,
                'properties': {
                    'userid': author,
                    'nickname': '',
                    'ip': detailed_content.get('ip', '')
                }
            }
            writer._upsert_user_vertex(author_data)
            
            # 建立發文關係
            writer._upsert_posted_edge(author, post_id, {
                'time': detailed_content.get('date', ''),
                'ip': detailed_content.get('ip', '')
            })
        
        # 更新推文
        new_comments_count = 0
        for comment in comments:
            comment_author = comment.get('author', '')
            if not comment_author:
                continue
            
            # 更新推文者
            commenter_data = {
                'id': comment_author,
                'properties': {
                    'userid': comment_author,
                    'nickname': '',
                    'ip': comment.get('ip', '')
                }
            }
            writer._upsert_user_vertex(commenter_data)
            
            # 建立推文關係
            comment_result = writer._upsert_commented_edge(
                comment_author, 
                post_id, 
                {
                    'comment_type': comment.get('type', ''),
                    'time': comment.get('time', ''),
                    'content': comment.get('content', ''),
                    'ip': comment.get('ip', '')
                }
            )
            
            if comment_result.get('new', False):
                new_comments_count += 1
        
        print(f"✅ 推文更新完成，新增 {new_comments_count} 則推文")
        
        return True
        
    except Exception as e:
        print(f"❌ 重新爬取失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理資源
        try:
            if client.is_logged_in:
                client.logout()
            writer.close()
        except:
            pass

def recrawl_recent_posts(board_name, hours=2):
    """重新爬取最近幾小時的文章推文
    
    Args:
        board_name (str): 看板名稱
        hours (int): 最近幾小時
    
    Returns:
        bool: 是否成功
    """
    print(f"🔄 重新爬取 {board_name} 看板最近 {hours} 小時的文章推文")
    
    client = PTTClient()
    
    try:
        # 登入 PTT
        if not client.login():
            print("❌ PTT 登入失敗")
            return False
        
        print("✅ PTT 登入成功")
        
        # 取得最新文章列表
        print("📋 取得最新文章列表...")
        posts = client.get_board_posts(board_name, max_posts=20)
        
        if not posts:
            print("❌ 無法取得文章列表")
            return False
        
        print(f"✅ 取得 {len(posts)} 篇文章")
        
        # 過濾最近的文章（簡化版，實際應該解析日期）
        recent_posts = posts[:10]  # 取前10篇作為最近文章
        
        success_count = 0
        for i, post in enumerate(recent_posts, 1):
            post_id = post.get('aid')
            title = post.get('title', 'N/A')[:50]
            
            print(f"\n📄 處理文章 {i}/{len(recent_posts)}: {title}...")
            
            if post_id:
                if recrawl_post_comments(post_id, board_name):
                    success_count += 1
                else:
                    print(f"⚠️ 文章 {post_id} 重新爬取失敗")
            else:
                print("⚠️ 文章沒有 AID，跳過")
        
        print(f"\n🎉 完成！成功重新爬取 {success_count}/{len(recent_posts)} 篇文章")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 批量重新爬取失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            if client.is_logged_in:
                client.logout()
        except:
            pass

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python manual_recrawl.py <board_name>                    # 重新爬取看板最近文章")
        print("  python manual_recrawl.py <board_name> <post_id>          # 重新爬取特定文章")
        print("")
        print("範例:")
        print("  python manual_recrawl.py stock                          # 重新爬取 stock 看板")
        print("  python manual_recrawl.py stock stock.1eLC2coG           # 重新爬取特定文章")
        sys.exit(1)
    
    board_name = sys.argv[1]
    
    if len(sys.argv) >= 3:
        # 重新爬取特定文章
        post_id = sys.argv[2]
        success = recrawl_post_comments(post_id, board_name)
    else:
        # 重新爬取看板最近文章
        success = recrawl_recent_posts(board_name)
    
    if success:
        print("\n🎉 重新爬取完成！")
    else:
        print("\n❌ 重新爬取失敗")
        sys.exit(1)

#!/usr/bin/env python3
"""
調試 PyPtt 文章結構，找出推文資料的正確位置
"""

import sys
import os
import json
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config

def debug_pyptt_post_structure():
    """調試 PyPtt 文章結構"""
    print("=" * 60)
    print("調試 PyPtt 文章結構")
    print("=" * 60)
    
    try:
        import PyPtt
        
        # 建立 PyPtt 物件
        ptt_bot = PyPtt.API()
        
        print("1. 登入 PTT...")
        ptt_bot.login(
            ptt_id=Config.PTT_USERNAME,
            ptt_pw=Config.PTT_PASSWORD,
            kick_other_session=True
        )
        print("✅ 登入成功")
        
        # 測試 felaray 的文章 (AID: 1eKwtbNX)
        print("\n2. 測試取得 felaray 的文章...")
        board_name = "Test"
        aid = "1eKwtbNX"  # felaray 的文章 AID
        
        print(f"嘗試取得文章 AID: {aid}")
        
        # 方法1: 使用 get_post with aid
        try:
            print("\n--- 方法1: get_post(board, aid) ---")
            post = ptt_bot.get_post(board=board_name, aid=aid)
            
            if post:
                print(f"✅ 成功取得文章")
                print(f"文章類型: {type(post)}")
                
                if isinstance(post, dict):
                    print("\n文章字典內容:")
                    for key, value in post.items():
                        print(f"  {key}: {value} (類型: {type(value)})")
                        
                        # 如果是列表，顯示列表內容
                        if isinstance(value, list) and value:
                            print(f"    列表長度: {len(value)}")
                            for i, item in enumerate(value[:3]):
                                print(f"      項目 {i+1}: {item} (類型: {type(item)})")
                                if hasattr(item, '__dict__'):
                                    print(f"        屬性: {item.__dict__}")
                else:
                    print("\n文章物件屬性:")
                    for attr in dir(post):
                        if not attr.startswith('_'):
                            try:
                                value = getattr(post, attr)
                                if not callable(value):
                                    print(f"  {attr}: {value} (類型: {type(value)})")
                                    
                                    # 如果是列表，顯示列表內容
                                    if isinstance(value, list) and value:
                                        print(f"    列表長度: {len(value)}")
                                        for i, item in enumerate(value[:3]):
                                            print(f"      項目 {i+1}: {item} (類型: {type(item)})")
                                            if hasattr(item, '__dict__'):
                                                print(f"        屬性: {item.__dict__}")
                            except Exception as e:
                                print(f"  {attr}: 無法取得值 ({e})")
            else:
                print("❌ 無法取得文章")
                
        except Exception as e:
            print(f"❌ 方法1失敗: {e}")
        
        # 方法2: 先進入看板，再取得文章
        try:
            print("\n--- 方法2: goto_board + get_post ---")
            ptt_bot.goto_board(board_name)
            print(f"✅ 已進入 {board_name} 看板")
            
            post = ptt_bot.get_post(board=board_name, aid=aid)
            
            if post:
                print(f"✅ 成功取得文章")
                print(f"文章類型: {type(post)}")
                
                # 檢查所有可能的推文相關屬性
                comment_attrs = [
                    'comments', 'pushes', 'responses', 'replies', 'push_list',
                    'comment_list', 'push_count', 'all_comments', 'msgs'
                ]
                
                print("\n檢查推文相關屬性:")
                for attr in comment_attrs:
                    value = None
                    if isinstance(post, dict):
                        value = post.get(attr)
                    else:
                        value = getattr(post, attr, None)
                    
                    if value is not None:
                        print(f"  💬 {attr}: {value} (類型: {type(value)})")
                        if isinstance(value, list):
                            print(f"     列表長度: {len(value)}")
                            for i, item in enumerate(value):
                                print(f"       項目 {i+1}: {item}")
                                if hasattr(item, '__dict__'):
                                    print(f"         屬性: {item.__dict__}")
                        elif hasattr(value, '__dict__'):
                            print(f"     物件屬性: {value.__dict__}")
                    else:
                        print(f"  ❌ {attr}: None")
                
                # 如果是字典，檢查所有鍵值
                if isinstance(post, dict):
                    print("\n所有字典鍵值:")
                    for key, value in post.items():
                        if 'comment' in key.lower() or 'push' in key.lower() or 'reply' in key.lower():
                            print(f"  🔍 {key}: {value}")
                
                # 如果是物件，檢查所有屬性
                else:
                    print("\n所有物件屬性:")
                    for attr in dir(post):
                        if not attr.startswith('_') and ('comment' in attr.lower() or 'push' in attr.lower() or 'reply' in attr.lower()):
                            try:
                                value = getattr(post, attr)
                                print(f"  🔍 {attr}: {value}")
                            except:
                                print(f"  🔍 {attr}: 無法取得")
                
            else:
                print("❌ 無法取得文章")
                
        except Exception as e:
            print(f"❌ 方法2失敗: {e}")
        
        # 方法3: 嘗試使用索引取得文章
        try:
            print("\n--- 方法3: 使用索引取得文章 ---")
            
            # 取得最新索引
            newest_index = ptt_bot.get_newest_index(
                index_type=PyPtt.NewIndex.BOARD,
                board=board_name
            )
            print(f"最新索引: {newest_index}")
            
            # 嘗試找到 felaray 的文章
            for i in range(max(1, newest_index - 10), newest_index + 1):
                try:
                    post = ptt_bot.get_post(board=board_name, index=i)
                    if post:
                        author = ""
                        if isinstance(post, dict):
                            author = post.get('author', '')
                        else:
                            author = getattr(post, 'author', '')
                        
                        if 'felaray' in author.lower():
                            print(f"\n找到 felaray 文章 (索引 {i}):")
                            print(f"作者: {author}")
                            
                            # 檢查推文
                            comment_attrs = ['comments', 'pushes', 'responses', 'replies', 'push_list']
                            for attr in comment_attrs:
                                value = None
                                if isinstance(post, dict):
                                    value = post.get(attr)
                                else:
                                    value = getattr(post, attr, None)
                                
                                if value is not None:
                                    print(f"  💬 {attr}: {value}")
                                    if isinstance(value, list) and value:
                                        print(f"     找到 {len(value)} 個項目:")
                                        for j, item in enumerate(value):
                                            print(f"       {j+1}: {item}")
                            
                            break
                except:
                    continue
                    
        except Exception as e:
            print(f"❌ 方法3失敗: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("開始調試 PyPtt 文章結構...")
    
    # 檢查配置
    config_errors = Config.validate_config()
    if config_errors:
        print("❌ 配置錯誤:")
        for error in config_errors:
            print(f"   - {error}")
        exit(1)
    
    print(f"✅ 使用帳號: {Config.PTT_USERNAME}")
    
    success = debug_pyptt_post_structure()
    
    if success:
        print("\n🎉 調試完成！")
    else:
        print("\n⚠️ 調試失敗")

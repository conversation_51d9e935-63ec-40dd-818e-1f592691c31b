import PyPtt
import time
import os
from datetime import datetime

# 設定帳號密碼（建議從環境變數讀取）
PTT_ID = os.getenv('PTT_USERNAME', 'your_username')
PTT_PASSWORD = os.getenv('PTT_PASSWORD', 'your_password')

def ptt_login_example():
    """PTT 登入範例"""
    
    # 建立 PTT 物件
    ptt_bot = PyPtt.PTT()
    
    try:
        print("開始連接PTT...")
        
        # 登入PTT
        # 第三個參數 kick_other_session=True 表示踢掉其他連線
        ptt_bot.login(
            ptt_id=PTT_ID,
            ptt_pw=PTT_PASSWORD,
            kick_other_session=True
        )
        
        print(f"✅ 成功登入PTT，使用者: {PTT_ID}")
        
        # 取得使用者資訊
        user_info = ptt_bot.get_user()
        print(f"📊 使用者資訊:")
        print(f"   ID: {user_info.ptt_id}")
        print(f"   經驗值: {user_info.career}")
        print(f"   等級: {user_info.level}")
        print(f"   註冊日期: {user_info.register_time}")
        
        # 取得目前在線人數
        online_user = ptt_bot.get_call_status()
        print(f"🌐 目前線上人數: {online_user}")
        
        # 進入看板範例
        print("\n📋 進入Test看板...")
        ptt_bot.goto_board('Test')
        
        # 取得看板資訊
        board_info = ptt_bot.get_board_info()
        print(f"   看板名稱: {board_info.board}")
        print(f"   看板標題: {board_info.title}")
        print(f"   板主: {board_info.moderators}")
        print(f"   線上人數: {board_info.online_user}")
        
        # 發文範例（請謹慎使用，避免洗版）
        post_example = False  # 設為 True 才會真的發文
        
        if post_example:
            print("\n✍️  發文測試...")
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            post_result = ptt_bot.post(
                board='Test',
                title=f'[測試] 自動發文測試 {current_time}',
                content=f'''這是一個自動發文的測試

發文時間: {current_time}
測試功能: PyPtt 自動登入發文

請忽略此測試文章，謝謝！

--
※ 發信站: 批踢踢實業坊(ptt.cc)
◆ From: 自動發文程式
''',
                sign_file=1  # 使用第一個簽名檔
            )
            
            if post_result == PyPtt.PostResult.Success:
                print("✅ 發文成功！")
            else:
                print(f"❌ 發文失敗: {post_result}")
        
        # 等待一下
        time.sleep(2)
        
    except PyPtt.exceptions.LoginError as e:
        print(f"❌ 登入失敗: {e}")
        return False
        
    except PyPtt.exceptions.ConnectionClosed as e:
        print(f"❌ 連線中斷: {e}")
        return False
        
    except PyPtt.exceptions.UnregisteredUser as e:
        print(f"❌ 未註冊使用者: {e}")
        return False
        
    except Exception as e:
        print(f"❌ 發生錯誤: {e}")
        return False
        
    finally:
        # 登出（重要！）
        try:
            print("\n🔓 登出PTT...")
            ptt_bot.logout()
            print("✅ 成功登出")
        except Exception as e:
            print(f"⚠️  登出時發生錯誤: {e}")
    
    return True

def advanced_ptt_example():
    """進階PTT操作範例"""
    
    ptt_bot = PyPtt.PTT()
    
    try:
        # 登入
        ptt_bot.login(PTT_ID, PTT_PASSWORD, kick_other_session=True)
        print("✅ 登入成功")
        
        # 搜尋文章範例
        print("\n🔍 搜尋文章...")
        ptt_bot.goto_board('Gossiping')  # 進入八卦板
        
        # 搜尋標題包含"問卦"的文章（最多10篇）
        search_list = ptt_bot.search_post(
            search_type=PyPtt.PostSearchType.KEYWORD,
            board='Gossiping',
            search_condition='問卦',
            search_list=10
        )
        
        print(f"🔍 找到 {len(search_list)} 篇文章:")
        for i, post in enumerate(search_list[:3]):  # 只顯示前3篇
            print(f"   {i+1}. {post.title}")
            print(f"      作者: {post.author} | 日期: {post.date}")
        
        # 取得最新文章列表
        print("\n📄 取得最新文章...")
        newest_list = ptt_bot.get_newest_index(
            index=PyPtt.NewIndex.BOARD,
            board='Test'
        )
        
        print(f"📄 最新文章數量: {len(newest_list)}")
        for i, post in enumerate(newest_list[:3]):  # 只顯示前3篇
            print(f"   {i+1}. {post.title}")
            print(f"      作者: {post.author}")
        
    except Exception as e:
        print(f"❌ 發生錯誤: {e}")
        
    finally:
        try:
            ptt_bot.logout()
            print("✅ 登出成功")
        except:
            pass

if __name__ == "__main__":
    print("🚀 PyPtt 登入測試開始...")
    print("="*50)
    
    # 檢查是否設定了帳號密碼
    if PTT_ID == 'your_username' or PTT_PASSWORD == 'your_password':
        print("⚠️  請先設定環境變數 PTT_USERNAME 和 PTT_PASSWORD")
        print("   或直接修改程式碼中的帳號密碼")
        exit(1)
    
    # 基本登入測試
    success = ptt_login_example()
    
    if success:
        print("\n" + "="*50)
        print("🔥 進階功能測試...")
        advanced_ptt_example()
    
    print("\n🎉 測試完成！")
{% extends "base.html" %}

{% block title %}系統狀態 - PTT 圖形分析系統{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-gear"></i> 系統狀態
        </h1>
        <p class="lead">監控系統運行狀態和資料庫連接情況。</p>
    </div>
</div>

<!-- 系統概覽 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="stats-number text-success" id="systemStatus">
                    <i class="bi bi-check-circle"></i>
                </div>
                <div class="text-muted">系統狀態</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="stats-number" id="totalVertices">-</div>
                <div class="text-muted">總頂點數</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="stats-number" id="totalEdges">-</div>
                <div class="text-muted">總邊數</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="stats-number" id="lastUpdate">-</div>
                <div class="text-muted">最後更新</div>
            </div>
        </div>
    </div>
</div>

<!-- 詳細狀態 -->
<div class="row">
    <!-- 資料庫狀態 -->
    <div class="col-md-6">
        <div class="card result-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-database"></i> 資料庫狀態
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshDatabaseStatus()">
                    <i class="bi bi-arrow-clockwise"></i> 重新整理
                </button>
            </div>
            <div class="card-body">
                <div id="databaseStatus">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">載入中...</span>
                        </div>
                        <p class="mt-2">正在檢查資料庫狀態...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API 狀態 -->
    <div class="col-md-6">
        <div class="card result-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-cloud"></i> API 狀態
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshApiStatus()">
                    <i class="bi bi-arrow-clockwise"></i> 重新整理
                </button>
            </div>
            <div class="card-body">
                <div id="apiStatus">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">載入中...</span>
                        </div>
                        <p class="mt-2">正在檢查 API 狀態...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系統配置 -->
<div class="card result-card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-sliders"></i> 系統配置
        </h5>
    </div>
    <div class="card-body">
        <div id="systemConfig">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">載入中...</span>
                </div>
                <p class="mt-2">正在載入系統配置...</p>
            </div>
        </div>
    </div>
</div>

<!-- 操作面板 -->
<div class="card result-card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-tools"></i> 系統操作
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="d-grid">
                    <button class="btn btn-outline-info" onclick="testPttConnection()">
                        <i class="bi bi-wifi"></i> 測試 PTT 連接
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-grid">
                    <button class="btn btn-outline-success" onclick="testDatabaseConnection()">
                        <i class="bi bi-database-check"></i> 測試資料庫連接
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-grid">
                    <button class="btn btn-outline-primary" onclick="refreshAllStatus()">
                        <i class="bi bi-arrow-clockwise"></i> 重新整理全部
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 操作結果 -->
<div id="operationResult"></div>
{% endblock %}

{% block extra_js %}
<script>
// 頁面載入時自動檢查狀態
document.addEventListener('DOMContentLoaded', function() {
    refreshAllStatus();
});

// 重新整理所有狀態
async function refreshAllStatus() {
    await Promise.all([
        refreshDatabaseStatus(),
        refreshApiStatus(),
        refreshSystemConfig()
    ]);
}

// 重新整理資料庫狀態
async function refreshDatabaseStatus() {
    const container = document.getElementById('databaseStatus');
    
    try {
        const response = await apiRequest('/api/graph-stats');
        
        if (response.success) {
            const data = response.data;
            
            // 更新頂點和邊數統計
            document.getElementById('totalVertices').textContent = 
                (data.vertices.users + data.vertices.ips + data.vertices.posts);
            document.getElementById('totalEdges').textContent = 
                (data.edges.posted + data.edges.commented + data.edges.used_ip + data.edges.from_ip);
            document.getElementById('lastUpdate').textContent = 
                new Date(data.updated_at).toLocaleString('zh-TW');
            
            const html = `
                <div class="row">
                    <div class="col-6">
                        <h6>頂點統計</h6>
                        <ul class="list-unstyled">
                            <li>使用者: <span class="badge bg-primary">${data.vertices.users}</span></li>
                            <li>IP 位址: <span class="badge bg-success">${data.vertices.ips}</span></li>
                            <li>文章: <span class="badge bg-warning">${data.vertices.posts}</span></li>
                        </ul>
                    </div>
                    <div class="col-6">
                        <h6>邊統計</h6>
                        <ul class="list-unstyled">
                            <li>發文: <span class="badge bg-info">${data.edges.posted}</span></li>
                            <li>推文: <span class="badge bg-secondary">${data.edges.commented}</span></li>
                            <li>使用 IP: <span class="badge bg-dark">${data.edges.used_ip}</span></li>
                            <li>來自 IP: <span class="badge bg-light text-dark">${data.edges.from_ip}</span></li>
                        </ul>
                    </div>
                </div>
                <div class="alert alert-success mt-3">
                    <i class="bi bi-check-circle"></i> 資料庫連接正常
                </div>
            `;
            
            container.innerHTML = html;
        } else {
            throw new Error(response.error || '未知錯誤');
        }
    } catch (error) {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> 資料庫連接失敗: ${error.message}
            </div>
        `;
        
        // 重置統計數字
        document.getElementById('totalVertices').textContent = '錯誤';
        document.getElementById('totalEdges').textContent = '錯誤';
        document.getElementById('lastUpdate').textContent = '無法取得';
    }
}

// 重新整理 API 狀態
async function refreshApiStatus() {
    const container = document.getElementById('apiStatus');
    
    const apiEndpoints = [
        { name: '健康檢查', url: '/api/system/health' },
        { name: '使用者列表', url: '/api/users?limit=1' },
        { name: 'IP 列表', url: '/api/ips?limit=1' }
    ];
    
    let html = '<div class="row">';
    
    for (const endpoint of apiEndpoints) {
        try {
            const response = await apiRequest(endpoint.url);
            const status = response.success !== false ? 'success' : 'danger';
            const icon = status === 'success' ? 'check-circle' : 'x-circle';
            
            html += `
                <div class="col-12 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>${endpoint.name}</span>
                        <span class="badge bg-${status}">
                            <i class="bi bi-${icon}"></i> ${status === 'success' ? '正常' : '異常'}
                        </span>
                    </div>
                </div>
            `;
        } catch (error) {
            html += `
                <div class="col-12 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>${endpoint.name}</span>
                        <span class="badge bg-danger">
                            <i class="bi bi-x-circle"></i> 錯誤
                        </span>
                    </div>
                </div>
            `;
        }
    }
    
    html += '</div>';
    container.innerHTML = html;
}

// 重新整理系統配置
async function refreshSystemConfig() {
    const container = document.getElementById('systemConfig');
    
    try {
        const response = await apiRequest('/status');
        
        const html = `
            <div class="row">
                <div class="col-md-6">
                    <h6>PTT 客戶端</h6>
                    <ul class="list-unstyled">
                        <li>連接狀態: <span class="badge ${response.ptt_client?.connected ? 'bg-success' : 'bg-secondary'}">
                            ${response.ptt_client?.connected ? '已連接' : '未連接'}
                        </span></li>
                        <li>登入狀態: <span class="badge ${response.ptt_client?.logged_in ? 'bg-success' : 'bg-secondary'}">
                            ${response.ptt_client?.logged_in ? '已登入' : '未登入'}
                        </span></li>
                        <li>最後操作: ${response.ptt_client?.last_operation || '無'}</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>配置狀態</h6>
                    <ul class="list-unstyled">
                        <li>配置有效: <span class="badge ${response.config_valid ? 'bg-success' : 'bg-danger'}">
                            ${response.config_valid ? '是' : '否'}
                        </span></li>
                        ${response.config_errors && response.config_errors.length > 0 ? 
                            `<li class="text-danger">錯誤: ${response.config_errors.join(', ')}</li>` : 
                            '<li class="text-success">配置正常</li>'
                        }
                    </ul>
                </div>
            </div>
        `;
        
        container.innerHTML = html;
    } catch (error) {
        container.innerHTML = `
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i> 無法載入系統配置: ${error.message}
            </div>
        `;
    }
}

// 測試 PTT 連接
async function testPttConnection() {
    const resultContainer = document.getElementById('operationResult');
    resultContainer.innerHTML = `
        <div class="alert alert-info">
            <i class="bi bi-hourglass-split"></i> 正在測試 PTT 連接...
        </div>
    `;
    
    try {
        const formData = new FormData();
        const response = await fetch('/test-login', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            resultContainer.innerHTML = `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> PTT 連接測試成功
                    <br><small>${result.message}</small>
                </div>
            `;
        } else {
            resultContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-x-circle"></i> PTT 連接測試失敗
                    <br><small>${result.message}</small>
                </div>
            `;
        }
    } catch (error) {
        resultContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-x-circle"></i> PTT 連接測試失敗: ${error.message}
            </div>
        `;
    }
}

// 測試資料庫連接
async function testDatabaseConnection() {
    const resultContainer = document.getElementById('operationResult');
    resultContainer.innerHTML = `
        <div class="alert alert-info">
            <i class="bi bi-hourglass-split"></i> 正在測試資料庫連接...
        </div>
    `;
    
    try {
        const response = await apiRequest('/api/graph-stats');
        
        if (response.success) {
            resultContainer.innerHTML = `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> 資料庫連接測試成功
                    <br><small>找到 ${response.data.vertices.users + response.data.vertices.ips + response.data.vertices.posts} 個頂點</small>
                </div>
            `;
        } else {
            throw new Error(response.error || '未知錯誤');
        }
    } catch (error) {
        resultContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-x-circle"></i> 資料庫連接測試失敗: ${error.message}
            </div>
        `;
    }
}
</script>
{% endblock %}

# 📌 PTT 自動登入系統 – 擴展功能說明書

本文件說明現有系統可進一步擴充的功能模組與設計構想，作為 AI 協作開發與未來版本規劃之依據。

---

## 📖 專案現況摘要

目前系統具備以下能力：

- ✅ 基於 Flask 的 Web 應用
- ✅ 使用 PyPtt 完成 PTT 登入功能
- ✅ 支援 Web 登入測試與狀態查詢
- ✅ 日誌系統與錯誤處理完備
- ✅ 可部署於 Azure 並支援環境變數配置

---

## 🚀 擴展功能需求

### 1️⃣ 每日自動爬文任務（ETL）

| 功能 | 說明 |
|------|------|
| 登入 PTT | 自動以指定帳號每日登入一次 |
| 指定看板 | 支援 array 格式設定，如 `['Gossiping', 'C_Chat']` |
| 抓取資訊 | 每篇文章擷取發文者、IP（如有）、推文者清單 |
| 資料結構化 | 將擷取資訊整理為 JSON 並傳送至圖資料庫 |
| 自動登出 | 任務完成後安全登出 PTT |

> ✅ 可透過 Azure Function / cronjob 自動觸發

---

### 2️⃣ 圖形資料庫整合（Azure Cosmos DB - Gremlin API）

| 項目 | 說明 |
|------|------|
| Vertex 頂點類型 | `User`、`IP`、`Post` |
| Edge 邊類型 | `POSTED`、`COMMENTED`、`USED_IP`、`FROM_IP` |
| 動態更新 | 每日爬文任務後自動 upsert 至 Cosmos DB |
| 擴展彈性 | 可整合 proxy IP、國家分析、帳號分身偵測等邏輯 |

---

### 3️⃣ Graph API 查詢服務（Graph Query Web API）

| API 路由 | 功能說明 |
|----------|----------|
| `GET /api/user-links?username=abc` | 查詢使用者互動關聯 |
| `GET /api/ip-users?ip=140.112.x.x` | 查詢使用過該 IP 的帳號 |
| `GET /api/post-interactions?post_id=board.M.1234` | 查詢該文互動網絡 |

> ✅ 可用 Flask Blueprint 或 FastAPI 擴建

---

### 4️⃣ 前端可視化介面（Web UI）

| 頁面 | 功能 |
|------|------|
| 使用者關聯查詢頁 | 顯示該帳號與他人互動網圖 |
| IP 分析頁 | 顯示使用過該 IP 的帳號與時間點 |
| 文章互動頁 | 顯示留言者、推文者的圖形結構 |

- ⚙️ 技術建議：Vue 3 / React + Cytoscape.js

---

## 🧱 預期目錄結構（擴充後）

```
ptt_graph_project/
├── app.py               # Flask app 主入口
├── ptt_client.py        # 登入與爬蟲核心
├── crawler.py           # 爬文與結構化資料
├── graph_writer.py      # Graph DB 資料寫入
├── graph_query.py       # Graph 查詢封裝
├── scheduler.py         # 定時觸發主腳本
├── api/                 # REST API 路由模組
├── templates/           # HTML 模板
├── static/              # 前端資源
├── config.py
└── ...
```

---

## 📈 延伸功能建議（未來可選）

- 🧠 帳號分身偵測（根據 IP 與互動圖）
- 📊 每日爬文統計報表（可視化圖形 UI）
- 🔐 使用者登入驗證（限制查詢存取）
- 💬 訊息通知（例如爬蟲失敗通知 LINE / Discord）

---

## 📎 協作建議（給 AI / 工程人員）

- 可先從 `ptt_client.py` 擴充 `get_board_posts()` 與 `get_comments()` 方法開始
- 所有圖資料請統一透過 `graph_writer.py` 實作
- API 設計需回傳 JSON 結構並支援 CORS
- 前端建議整合 REST API 或 WebSocket（互動圖擴展時用）

---

> 🔄 本文件建議與 `README.md` 搭配閱讀，做為功能開發與任務拆解之基礎依據。

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清空 Cosmos DB 圖形資料庫
"""

import sys
from gremlin_python.driver import client, serializer
from config import Config

# 設定正確的編碼
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')

def clear_database():
    """清空資料庫中的所有資料"""
    print("=" * 60)
    print("清空 Cosmos DB 圖形資料庫")
    print("=" * 60)
    
    try:
        # 建立連線
        endpoint = Config.COSMOS_DB_ENDPOINT
        if not endpoint.endswith('/'):
            endpoint += '/'
            
        gclient = client.Client(
            endpoint,
            'g',
            username=f"/dbs/{Config.COSMOS_DB_DATABASE}/colls/{Config.COSMOS_DB_COLLECTION}",
            password=Config.COSMOS_DB_KEY,
            message_serializer=serializer.GraphSONSerializersV2d0()
        )
        
        print("✅ 連線建立成功")
        
        # 檢查現有資料
        print("\n檢查現有資料...")
        vertex_count = list(gclient.submit("g.V().count()").all().result())[0]
        edge_count = list(gclient.submit("g.E().count()").all().result())[0]
        
        print(f"目前頂點數: {vertex_count}")
        print(f"目前邊數: {edge_count}")
        
        if vertex_count == 0 and edge_count == 0:
            print("✅ 資料庫已經是空的")
            return True
        
        # 確認是否要清空
        print(f"\n⚠️ 即將刪除 {vertex_count} 個頂點和 {edge_count} 條邊")
        confirm = input("確定要清空資料庫嗎？(y/N): ").strip().lower()
        
        if confirm != 'y':
            print("❌ 取消清空操作")
            return False
        
        print("\n開始清空資料庫...")
        
        # 刪除所有邊
        if edge_count > 0:
            print("刪除所有邊...")
            gclient.submit("g.E().drop()").all().result()
            print("✅ 所有邊已刪除")
        
        # 刪除所有頂點
        if vertex_count > 0:
            print("刪除所有頂點...")
            gclient.submit("g.V().drop()").all().result()
            print("✅ 所有頂點已刪除")
        
        # 驗證清空結果
        print("\n驗證清空結果...")
        final_vertex_count = list(gclient.submit("g.V().count()").all().result())[0]
        final_edge_count = list(gclient.submit("g.E().count()").all().result())[0]
        
        print(f"剩餘頂點數: {final_vertex_count}")
        print(f"剩餘邊數: {final_edge_count}")
        
        if final_vertex_count == 0 and final_edge_count == 0:
            print("🎉 資料庫清空成功！")
            return True
        else:
            print("❌ 資料庫清空失敗")
            return False
            
    except Exception as e:
        print(f"❌ 清空資料庫失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            gclient.close()
        except:
            pass

if __name__ == "__main__":
    success = clear_database()
    if success:
        print("\n✅ 資料庫已清空，可以開始測試範例")
    else:
        print("\n❌ 清空失敗，請檢查錯誤訊息")

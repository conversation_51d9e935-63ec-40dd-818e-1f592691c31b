{% extends "base.html" %}

{% block title %}文章互動分析 - PTT 圖形分析系統{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-chat-dots"></i> 文章互動分析
        </h1>
        <p class="lead">分析 PTT 文章的互動情況，包括推文、噓文等互動關係。</p>
    </div>
</div>

<!-- 搜尋表單 -->
<div class="search-form">
    <form id="postSearchForm">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="postId" class="form-label">文章 ID</label>
                    <input type="text" class="form-control" id="postId" name="postId" 
                           placeholder="輸入文章 ID (例如: Test.1eKGdnIg)" required>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label for="includeComments" class="form-label">包含推文</label>
                    <select class="form-select" id="includeComments" name="includeComments">
                        <option value="true" selected>是</option>
                        <option value="false">否</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 分析文章
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearResults()">
                            <i class="bi bi-arrow-clockwise"></i> 清除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- 載入狀態 -->
<div id="loadingSpinner" class="loading-spinner">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">載入中...</span>
    </div>
    <p class="mt-2">正在分析文章互動...</p>
</div>

<!-- 結果區域 -->
<div id="resultsContainer" style="display: none;">
    <!-- 文章資訊 -->
    <div class="card result-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-file-text"></i> 文章資訊
            </h5>
        </div>
        <div class="card-body">
            <div id="postInfo"></div>
        </div>
    </div>

    <!-- 統計摘要 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="stats-number" id="totalComments">0</div>
                    <div class="text-muted">總推文數</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="stats-number" id="uniqueUsers">0</div>
                    <div class="text-muted">參與使用者</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="stats-number" id="pushCount">0</div>
                    <div class="text-muted">推</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="stats-number" id="booCount">0</div>
                    <div class="text-muted">噓</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 互動圖形 -->
    <div class="card result-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-diagram-3"></i> 互動關係圖
            </h5>
            <div class="graph-controls">
                <button class="btn btn-sm btn-outline-primary" onclick="fitGraph()">
                    <i class="bi bi-arrows-fullscreen"></i> 適應視窗
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="resetGraph()">
                    <i class="bi bi-arrow-clockwise"></i> 重置佈局
                </button>
            </div>
        </div>
        <div class="card-body">
            <div id="interactionGraph" class="graph-container"></div>
        </div>
    </div>

    <!-- 推文列表 -->
    <div class="card result-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-chat-left-text"></i> 推文詳情
            </h5>
        </div>
        <div class="card-body">
            <div id="commentsList"></div>
        </div>
    </div>
</div>

<!-- 錯誤訊息 -->
<div id="errorContainer"></div>
{% endblock %}

{% block extra_js %}
<script>
let interactionGraph = null;

// 表單提交處理
document.getElementById('postSearchForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const postId = document.getElementById('postId').value.trim();
    const includeComments = document.getElementById('includeComments').value;
    
    if (!postId) {
        showError('請輸入文章 ID', 'errorContainer');
        return;
    }
    
    // 顯示載入狀態
    showLoading('loadingSpinner');
    document.getElementById('resultsContainer').style.display = 'none';
    document.getElementById('errorContainer').innerHTML = '';
    
    try {
        // 呼叫 API
        const response = await apiRequest(`/api/post-interactions?post_id=${encodeURIComponent(postId)}&include_comments=${includeComments}`);
        
        if (response.success) {
            displayPostAnalysis(response.data);
        } else {
            showError(response.error || '查詢失敗', 'errorContainer');
        }
    } catch (error) {
        showError(`查詢失敗: ${error.message}`, 'errorContainer');
    } finally {
        hideLoading('loadingSpinner');
    }
});

// 顯示文章分析結果
function displayPostAnalysis(data) {
    // 顯示文章資訊
    displayPostInfo(data.post || {});
    
    // 更新統計數字
    const comments = data.comments || [];
    document.getElementById('totalComments').textContent = comments.length;
    
    const uniqueUsers = new Set(comments.map(c => c.author)).size;
    document.getElementById('uniqueUsers').textContent = uniqueUsers;
    
    const pushCount = comments.filter(c => c.type === '推').length;
    document.getElementById('pushCount').textContent = pushCount;
    
    const booCount = comments.filter(c => c.type === '噓').length;
    document.getElementById('booCount').textContent = booCount;
    
    // 建立互動圖
    createInteractionGraph(data);
    
    // 顯示推文列表
    displayCommentsList(comments);
    
    // 顯示結果區域
    document.getElementById('resultsContainer').style.display = 'block';
}

// 顯示文章資訊
function displayPostInfo(post) {
    const container = document.getElementById('postInfo');
    
    const html = `
        <div class="row">
            <div class="col-md-8">
                <h5>${post.title || '無標題'}</h5>
                <p><strong>作者:</strong> ${post.author || '未知'}</p>
                <p><strong>看板:</strong> ${post.board || '未知'}</p>
                <p><strong>日期:</strong> ${post.date || '未知'}</p>
            </div>
            <div class="col-md-4">
                <p><strong>文章 ID:</strong> ${post.post_id || '未知'}</p>
                <p><strong>內容長度:</strong> ${post.content_length || 0} 字元</p>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

// 建立互動關係圖
function createInteractionGraph(data) {
    const container = document.getElementById('interactionGraph');
    const elements = [];
    
    // 添加文章節點
    const post = data.post || {};
    elements.push({
        data: { 
            id: 'post', 
            label: post.title || '文章',
            type: 'post'
        }
    });
    
    // 添加作者節點
    if (post.author) {
        elements.push({
            data: { 
                id: post.author, 
                label: post.author,
                type: 'user'
            }
        });
        
        elements.push({
            data: { 
                source: post.author, 
                target: 'post',
                type: 'authored'
            }
        });
    }
    
    // 添加推文者節點和邊
    const comments = data.comments || [];
    const userCommentCounts = {};
    
    comments.forEach((comment, index) => {
        const userId = comment.author;
        if (!userId) return;
        
        // 統計使用者推文數
        userCommentCounts[userId] = (userCommentCounts[userId] || 0) + 1;
        
        // 添加使用者節點（如果還沒有）
        if (!elements.find(e => e.data.id === userId)) {
            elements.push({
                data: { 
                    id: userId, 
                    label: userId,
                    type: 'user'
                }
            });
        }
        
        // 添加推文邊
        elements.push({
            data: { 
                source: userId, 
                target: 'post',
                type: 'commented',
                commentType: comment.type,
                id: `comment_${index}`
            }
        });
    });
    
    // 建立 Cytoscape 圖形
    interactionGraph = cytoscape({
        container: container,
        elements: elements,
        style: [
            {
                selector: 'node',
                style: {
                    'background-color': '#007bff',
                    'label': 'data(label)',
                    'text-valign': 'center',
                    'text-halign': 'center',
                    'color': 'white',
                    'text-outline-width': 2,
                    'text-outline-color': '#000',
                    'font-size': '12px',
                    'width': 60,
                    'height': 60
                }
            },
            {
                selector: 'node[type="post"]',
                style: {
                    'background-color': '#ffc107',
                    'shape': 'rectangle',
                    'width': 100,
                    'height': 60
                }
            },
            {
                selector: 'node[type="user"]',
                style: {
                    'background-color': '#007bff'
                }
            },
            {
                selector: 'edge',
                style: {
                    'width': 2,
                    'line-color': '#ccc',
                    'target-arrow-color': '#ccc',
                    'target-arrow-shape': 'triangle',
                    'curve-style': 'bezier'
                }
            },
            {
                selector: 'edge[type="authored"]',
                style: {
                    'line-color': '#dc3545',
                    'target-arrow-color': '#dc3545',
                    'width': 3
                }
            },
            {
                selector: 'edge[commentType="推"]',
                style: {
                    'line-color': '#28a745',
                    'target-arrow-color': '#28a745'
                }
            },
            {
                selector: 'edge[commentType="噓"]',
                style: {
                    'line-color': '#dc3545',
                    'target-arrow-color': '#dc3545'
                }
            }
        ],
        layout: {
            name: 'cose-bilkent',
            animate: true,
            animationDuration: 1000
        }
    });
}

// 顯示推文列表
function displayCommentsList(comments) {
    const container = document.getElementById('commentsList');
    
    if (comments.length === 0) {
        container.innerHTML = '<div class="text-muted">無推文記錄</div>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr><th>類型</th><th>使用者</th><th>內容</th><th>時間</th></tr></thead><tbody>';
    
    comments.forEach(comment => {
        const typeClass = comment.type === '推' ? 'text-success' : 
                         comment.type === '噓' ? 'text-danger' : 'text-info';
        
        html += `
            <tr>
                <td><span class="badge ${typeClass === 'text-success' ? 'bg-success' : 
                                        typeClass === 'text-danger' ? 'bg-danger' : 'bg-info'}">${comment.type || '→'}</span></td>
                <td>${comment.author || '匿名'}</td>
                <td>${comment.content || ''}</td>
                <td><small>${comment.time || ''}</small></td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// 圖形控制函數
function fitGraph() {
    if (interactionGraph) {
        interactionGraph.fit();
    }
}

function resetGraph() {
    if (interactionGraph) {
        interactionGraph.layout({ name: 'cose-bilkent', animate: true }).run();
    }
}

function clearResults() {
    document.getElementById('resultsContainer').style.display = 'none';
    document.getElementById('errorContainer').innerHTML = '';
    document.getElementById('postSearchForm').reset();
}
</script>
{% endblock %}

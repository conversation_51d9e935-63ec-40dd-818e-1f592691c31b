#!/usr/bin/env python3
"""
修正測試檔案中的 Unicode 符號問題
"""

import os
import re

def fix_unicode_in_file(filepath):
    """修正檔案中的 Unicode 符號"""
    if not os.path.exists(filepath):
        print(f"檔案不存在: {filepath}")
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替換特定的 Unicode 符號
        replacements = {
            '✓': '[OK]',
            '✗': '[FAIL]',
            '⚠️': '[WARN]',
            '🎉': '[SUCCESS]',
            '❌': '[ERROR]',
            '📄': '[FILE]',
            '💥': '[CRASH]',
            '⚠': '[WARN]',
            '❗': '[!]',
            '✅': '[OK]',
            '🔧': '[TOOL]',
            '📋': '[LIST]',
            '🚀': '[ROCKET]',
            '📖': '[BOOK]',
            '🧪': '[TEST]',
            '📊': '[CHART]',
            '🤝': '[HAND]',
            '📝': '[NOTE]',
            '⚡': '[FAST]',
            '🔍': '[SEARCH]',
            '💡': '[IDEA]',
            '🎯': '[TARGET]'
        }

        for old, new in replacements.items():
            content = content.replace(old, new)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"[OK] 修正完成: {filepath}")
        return True
        
    except Exception as e:
        print(f"[FAIL] 修正失敗 {filepath}: {e}")
        return False

def main():
    """主函數"""
    print("修正測試檔案中的 Unicode 符號")
    print("=" * 40)
    
    test_files = [
        'deploy_check.py',
        'test_crawler.py',
        'test_graph_writer.py',
        'test_graph_query.py',
        'test_scheduler.py',
        'test_api.py',
        'test_cosmos_connection.py',
        'run_all_tests.py'
    ]
    
    fixed = 0
    total = len(test_files)
    
    for filepath in test_files:
        if fix_unicode_in_file(filepath):
            fixed += 1
    
    print("\n" + "=" * 40)
    print(f"修正結果: {fixed}/{total} 個檔案修正成功")
    
    if fixed == total:
        print("[SUCCESS] 所有檔案都已修正完成！")
        return 0
    else:
        print("[WARN] 部分檔案修正失敗，請檢查錯誤訊息。")
        return 1

if __name__ == "__main__":
    exit(main())
